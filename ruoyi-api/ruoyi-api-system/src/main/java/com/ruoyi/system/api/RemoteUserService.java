package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.ruoyi.system.api.factory.RemoteUserFallbackFactory;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE,
        fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService {
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source   请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public LoginUser getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source  请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public Boolean registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取用户列表
     */
    @PostMapping("/user/listNoPage")
    public List<SysUser> listNoPage(@RequestBody SysUserListDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 记录用户登录信息
     */
    @PostMapping("/user/recordLoginInfo")
    public Boolean recordLoginInfo(SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据用户ID查询用户完整信息
     *
     * @param userId 用户ID
     * @param source 请求来源
     * @return 用户完整信息，包含selection_management等所有字段
     */
    @GetMapping("/user/detail/{userId}")
    public SysUser selectUserById(@PathVariable("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取当前用户以及他下级部门的用户信息
     */
    @GetMapping("/user/get-user-level")
    public List<SysUserVO> getUserLevel(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 客服数据-中文部客服数据
     */
    @GetMapping("/user/chinese-customer-service-data")
    List<ChineseCustomerServiceDataVO> selectChineseCustomerServiceData(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 客服数据-英文部客服数据
     */
    @GetMapping("/user/english-customer-service-data")
    List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceData(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取客服下拉框
     */
    @PostMapping("/user/customer-service-select")
    List<UserVO> customerServiceSelect(@RequestBody SysUserListDTO dto);
}

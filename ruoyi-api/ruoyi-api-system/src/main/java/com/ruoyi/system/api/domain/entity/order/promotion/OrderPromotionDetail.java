package com.ruoyi.system.api.domain.entity.order.promotion;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @TableName order_promotion_detail
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderPromotionDetail implements Serializable {
    private static final long serialVersionUID = -3891145988543416983L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动ID
     */
    @ApiModelProperty("活动ID")
    private Long activityId;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 视频ID
     */
    @ApiModelProperty("视频ID")
    private Long videoId;

    /**
     * 优惠金额（单位：￥）
     */
    @ApiModelProperty("优惠金额（单位：￥）")
    private BigDecimal discountAmount;

    /**
     * 优惠金额（单位：$）
     */
    @ApiModelProperty("优惠金额（单位：$）")
    private BigDecimal discountAmountDollar;

    /**
     * 优惠扣减类型（1：直减，2：折扣）
     */
    @ApiModelProperty(value = "优惠扣减类型（1：直减，2：折扣）")
    private Integer discountType;

    /**
     * 优惠数值
     */
    @ApiModelProperty(value = "优惠数值")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long createUserId;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty("修改人id")
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String updateUserName;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

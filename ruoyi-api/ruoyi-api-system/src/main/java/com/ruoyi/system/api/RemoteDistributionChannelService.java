package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderBatchDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO;
import com.ruoyi.system.api.factory.RemoteDistributionChannelFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:36
 */
@FeignClient(contextId = "remoteDistributionChannelService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteDistributionChannelFactory.class)
public interface RemoteDistributionChannelService {
//
//    /**
//     * 根据种草码 获取分销渠道信息
//     */
//    @GetMapping("/marketing/center/distribution/backend/get-by-seed-code")
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    BigDecimal getDistributionChannelBySeedCode(@RequestParam("seedCode") String seedCode, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
//
    /**
     * 根据种草码 获取渠道信息
     * @param seedCode
     * @param source
     * @return
     */
    @GetMapping("/marketing/center/distribution/backend/getFissionBrokeRageBySeedCode")
    ChannelBrokeRageVO getFissionBrokeRageBySeedCode(@RequestParam("seedCode") String seedCode, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据种草码 获取分销渠道信息
     */
    @GetMapping("/marketing/center/distribution/get-by-seed-code")
    DistributionChannel getDistributionChannelEntityBySeedCode(@RequestParam("seedCode") String seedCode, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 根据种草码列表获取 分销渠道列表
     * @param numbers
     * @param source
     * @return
     */
    @PostMapping("/marketing/center/distribution/inner/queryDistributionChannelsBySeedCodes")
    List<DistributionChannel> queryDistributionChannelsBySeedCodes(@RequestBody Collection<String> numbers, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * * 分销渠道结算-分销渠道列表
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/member-channel/inner/member-channel-list")
    List<OrderMemberChannelListVO> innerMemberChannelListByCondition(@RequestBody OrderMemberChannelListDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 保存渠道订单
     *
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/marketing/center/distribution/saveDistributionChannelOrder")
    String saveDistributionChannelOrder(@RequestBody DistributionChannelOrderDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量保存渠道订单
     *
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/marketing/center/distribution/saveBatchDistributionChannelOrder")
    String saveBatchDistributionChannelOrder(@RequestBody DistributionChannelOrderBatchDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}

package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 物流信息详情表
 * @TableName logistic_info_legacy
 */
@TableName(value ="logistic_info_legacy")
@Data
public class LogisticInfoLegacy implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 物流单号
     */
    @TableField(value = "number")
    private String number;

    /**
     * 事件描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 物流主状态
     */
    @TableField(value = "main_status")
    private String mainStatus;

    /**
     * 物流子状态
     */
    @TableField(value = "sub_status")
    private String subStatus;

    /**
     * 发生时间
     */
    @TableField(value = "cur_time")
    private Date curTime;

    /**
     * 当前节点在国家地区
     */
    @TableField(value = "country")
    private String country;

    /**
     * 当前节点所在州、省
     */
    @TableField(value = "state")
    private String state;

    /**
     * 当前节点所在城市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 当前节点所在街道
     */
    @TableField(value = "street")
    private String street;

    /**
     * 当前节点所在经度
     */
    @TableField(value = "longitude")
    private String longitude;

    /**
     * 当前节点所在纬度
     */
    @TableField(value = "latitude")
    private String latitude;

    /**
     * 当前所在地点
     */
    @TableField(value = "location")
    private String location;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LogisticInfoLegacy other = (LogisticInfoLegacy) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getNumber() == null ? other.getNumber() == null : this.getNumber().equals(other.getNumber()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getMainStatus() == null ? other.getMainStatus() == null : this.getMainStatus().equals(other.getMainStatus()))
            && (this.getSubStatus() == null ? other.getSubStatus() == null : this.getSubStatus().equals(other.getSubStatus()))
            && (this.getCurTime() == null ? other.getCurTime() == null : this.getCurTime().equals(other.getCurTime()))
            && (this.getCountry() == null ? other.getCountry() == null : this.getCountry().equals(other.getCountry()))
            && (this.getState() == null ? other.getState() == null : this.getState().equals(other.getState()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getStreet() == null ? other.getStreet() == null : this.getStreet().equals(other.getStreet()))
            && (this.getLongitude() == null ? other.getLongitude() == null : this.getLongitude().equals(other.getLongitude()))
            && (this.getLatitude() == null ? other.getLatitude() == null : this.getLatitude().equals(other.getLatitude()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNumber() == null) ? 0 : getNumber().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getMainStatus() == null) ? 0 : getMainStatus().hashCode());
        result = prime * result + ((getSubStatus() == null) ? 0 : getSubStatus().hashCode());
        result = prime * result + ((getCurTime() == null) ? 0 : getCurTime().hashCode());
        result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
        result = prime * result + ((getState() == null) ? 0 : getState().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getStreet() == null) ? 0 : getStreet().hashCode());
        result = prime * result + ((getLongitude() == null) ? 0 : getLongitude().hashCode());
        result = prime * result + ((getLatitude() == null) ? 0 : getLatitude().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", number=").append(number);
        sb.append(", description=").append(description);
        sb.append(", mainStatus=").append(mainStatus);
        sb.append(", subStatus=").append(subStatus);
        sb.append(", curTime=").append(curTime);
        sb.append(", country=").append(country);
        sb.append(", state=").append(state);
        sb.append(", city=").append(city);
        sb.append(", street=").append(street);
        sb.append(", longitude=").append(longitude);
        sb.append(", latitude=").append(latitude);
        sb.append(", location=").append(location);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
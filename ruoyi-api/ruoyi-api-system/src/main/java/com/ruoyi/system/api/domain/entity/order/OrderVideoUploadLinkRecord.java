package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-03-20 15:22:16
 */
@Data
@TableName("order_video_upload_link_record")
public class OrderVideoUploadLinkRecord implements Serializable {
    private static final long serialVersionUID = 6448682435478886454L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上传素材ID（FK：order_video_upload_link.id）
     */
    @ApiModelProperty(value = "上传素材ID（FK：order_video_upload_link.id）")
    private Long uploadLinkId;

    /**
     * 上传次数
     */
    @ApiModelProperty(value = "上传次数")
    private Integer count;

    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    private String needUploadLink;

    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号")
    private String uploadAccount;

    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图URI")
    private String videoCover;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 上传的链接
     */
    @ApiModelProperty(value = "上传的链接")
    private String uploadLink;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，4：上传失败）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，4：上传失败）")
    private Integer status;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    private Long uploadUserId;

    /**
     * 上传用户名称
     */
    @ApiModelProperty(value = "上传用户名称")
    private String uploadUserName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;

    /**
     * 操作备注
     */
    @ApiModelProperty(value = "操作备注")
    private String operateRemark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

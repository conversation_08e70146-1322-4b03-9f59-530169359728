package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 模特信息对象 model
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "模特信息对象 model")
@TableName("model")
@Data
public class Model implements Serializable {

    private static final long serialVersionUID = 2719526828856646617L;
    /**
     * 模特id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型", notes = "0:影响者,1:素人")
    @Excel(name = "模特类型", readConverterExp = "0:影响者,1:素人")
    private Integer type;

    /**
     * 模特类型_字典值
     */
    private String typeDict;

    /**
     * 姓名
     */
    @NotNull(message = "[姓名]不能为空")
    @ApiModelProperty(value = "姓名", required = true)
    @Excel(name = "姓名")
    private String name;

    @ApiModelProperty(value = "家庭id")
    private Long familyId;

    @ApiModelProperty(value = "亲属关系(0=发起人,1=母子,2=母女,3=夫妻,4=父子,5=父女,6=兄弟,7=姐妹,8=兄妹,9=姐弟")
    private Integer modelFamilyRelationship;

    @ApiModelProperty(value = "是否家庭模特发起者：0-否,1-是")
    private Integer isInitiator;

    @ApiModelProperty(value = "是否家庭模特：0-否,1-是")
    private Integer isFamilyModel;

    @ApiModelProperty(value = "加入家庭时间")
    private Date joinFamilyTime;

    /**
     * 性别(1:男,0:女)
     */
    @ApiModelProperty(value = "性别", notes = "1:男,0:女", required = true)
    @NotNull(message = "[性别]不能为空")
    @Excel(name = "性别", readConverterExp = "1:男,0:女")
    private Integer sex;

    /**
     * 性别_字典值(0:男,1:女)
     */
    private String sexDict;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
     */
    @ApiModelProperty(value = "年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）", notes = "1:婴幼儿,2:儿童,3:成年人,4:老年人", required = true)
    @NotNull(message = "[年龄层]不能为空")
    @Excel(name = "年龄层", readConverterExp = "1:婴幼儿,2:儿童,3:成年人,4:老年人")
    private Integer ageGroup;
    /**
     * 年龄层_字典值
     */
    private String ageGroupDict;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @NotNull(message = "[国家]不能为空")
    @ApiModelProperty(value = "国家", required = true, notes = "（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    @Excel(name = "国家")
    private Integer nation;

    /**
     * 国家_字典值
     */
    private String nationDict;


    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    @Excel(name = "收件人")
    private String recipient;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @Excel(name = "城市")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty(value = "州")
    @Excel(name = "州")
    private String state;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Excel(name = "邮编")
    private String zipcode;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @Excel(name = "详细地址")
    private String detailAddress;

    /**
     * 模特图片URI
     */
    @ApiModelProperty(value = "模特图片URI")
    @Excel(name = "模特图片URI")
    private String modelPic;

    /**
     * 生活场景照
     */
    @ApiModelProperty(value = "生活场景照")
    @Excel(name = "生活场景照")
    private String livePic;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "平台", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类", required = true)
    @NotNull(message = "[平台]不能为空")
    @Excel(name = "平台", readConverterExp = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private String platform;

    /**
     * 平台_字典值
     */
    private String platformDict;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度", notes = "0:一般模特,1:优质模特,2:中度模特", required = true)
//    @NotNull(message = "[合作深度]不能为空")
    @Excel(name = "模特等级", readConverterExp = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;


    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    @NotNull(message = "模特评分不能为空")
    @Excel(name = "模特等级", readConverterExp = "0:一般模特,1:优质模特,2:中度模特")
    private BigDecimal cooperationScore;

    /**
     * 待完成最高接受量
     */
    @ApiModelProperty(value = "待完成最高接受量")
    @Excel(name = "待完成最高接受量")
    private Integer acceptability;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    @Excel(name = "模特状态", readConverterExp = "0:正常,1:暂停,2:行程中,3:取消合作")
    private Integer status;

    /**
     * 状态变更时间
     */
    @ApiModelProperty(value = "状态变更时间")
    private Date statusTime;

    /**
     * 状态说明
     */
    @ApiModelProperty(value = "状态说明")
    private String statusExplain;

    /**
     * 亚马逊案例视频关联资源id（,拼接）
     */
    @TableField("amazon_video")
    private String amazonVideoId;
    /**
     * tiktok案例视频关联资源id（,拼接）
     */
    @TableField("tiktok_video")
    private String tiktokVideoId;

    /**
     * 取消合作类型(0-我们取消,1-模特取消)
     */
    @TableField(value = "cancel_cooperation_type", updateStrategy = FieldStrategy.IGNORED)
    private Integer cancelCooperationType;

    /**
     * 取消合作子类型(关联字典id)
     */
    @TableField(value = "cancel_cooperation_sub_type", updateStrategy = FieldStrategy.IGNORED)
    private String cancelCooperationSubType;

    /**
     * 置顶时间
     */
    @ApiModelProperty(value = "置顶时间")
    @Excel(name = "置顶时间")
    private Date topTime;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Integer sort;

    /**
     * 超时率
     */
    @ApiModelProperty(value = "超时率")
    private BigDecimal overtimeRate;
    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;

    @ApiModelProperty(value = "被拉黑数")
    private Long blacklistCount;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "同步提示语")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String syncMsg;

    /**
     * 是否展示（1:展示,0:不展示）
     */
    @ApiModelProperty(value = "是否展示（1:展示,0:不展示）")
    private Integer isShow;

    /**
     * 模特简介
     */
    @ApiModelProperty(value = "模特简介")
    private String about;

    /**
     * 有蜗牛照（1：有，0：没有）
     */
    @ApiModelProperty(value = "有蜗牛照（1：有，0：没有）")
    private Integer haveSnailPic;

    /**
     * 开发人ID
     */
    @ApiModelProperty(value = "开发人ID")
    private Long developerId;

    /**
     * 视频案例最新更新时间
     */
    @ApiModelProperty(value = "视频案例最新更新时间")
    private Date videoLastUpdateTime;

    /**
     * 标签最新更新时间
     */
    @ApiModelProperty(value = "标签最新更新时间")
    private Date tagLastUpdateTime;

    /**
     * 品类最新更新时间
     */
    @ApiModelProperty(value = "品类最新更新时间")
    private Date categoryLastUpdateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

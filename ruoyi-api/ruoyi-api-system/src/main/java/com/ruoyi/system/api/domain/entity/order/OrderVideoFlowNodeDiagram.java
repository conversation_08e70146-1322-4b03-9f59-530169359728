package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/14 17:18
 */
@Data
@TableName("order_video_flow_node_diagram")
public class OrderVideoFlowNodeDiagram implements Serializable {

    private static final long serialVersionUID = -5411133945029153739L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 当前节点（1:下单支付,2:匹配模特,3:商家发货,4:完成拍摄,5:商家确认,6:订单完成,10:取消订单）
     */
    @ApiModelProperty(value = "当前节点")
    private Integer node;

    /**
     * 节点完成时间
     */
    @ApiModelProperty(value = "节点完成时间")
    private Date time;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
}

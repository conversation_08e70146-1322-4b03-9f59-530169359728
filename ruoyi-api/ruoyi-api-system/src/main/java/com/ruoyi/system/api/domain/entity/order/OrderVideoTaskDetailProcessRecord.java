package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/11 17:58
 */
@Data
@TableName("order_video_task_detail_process_record")
public class OrderVideoTaskDetailProcessRecord {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String taskNum;

    /**
     * 记录时间
     */
    @ApiModelProperty(value = "记录时间")
    private Date time;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operateBy;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operateById;

    /**
     * 操作人类型（1：处理人，2：剪辑人，3：系统）
     */
    @ApiModelProperty(value = "操作人类型（1：处理人，2：剪辑人，3：系统）")
    private Integer operateByType;

    /**
     * 操作类型 详见OrderTaskDetailFlowOperateTypeEnum
     * @see com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum
     */
    @ApiModelProperty(value = "操作类型 详见OrderTaskDetailFlowOperateTypeEnum")
    private Integer operateType;

    /**
     * 完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）
     */
    @ApiModelProperty(value = "完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）")
    private Integer completionMode;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 对象存储键值
     */
    @ApiModelProperty(value = "对象存储键值")
    private String objectKeys;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}

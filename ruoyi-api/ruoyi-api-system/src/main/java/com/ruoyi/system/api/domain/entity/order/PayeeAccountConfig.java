package com.ruoyi.system.api.domain.entity.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 收款人账号配置表
* <AUTHOR>
 * @TableName payee_account_config
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PayeeAccountConfig implements Serializable {

    private static final long serialVersionUID = 7951348975216804220L;
    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotBlank(message="[收款账号名称]不能为空")
    @Size(max= 32,message="编码长度不能超过32")
    @ApiModelProperty("收款账号名称")
    @Length(max= 32,message="编码长度不能超过32")
    private String accountName;

    @NotNull(message="[账号类型]不能为空")
    @ApiModelProperty("账号类型（0-默认类型, 1-银行卡账号, 2-对公账号）")
    private Integer accountType;

    @NotBlank(message="[开户行名称]不能为空")
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("开户行名称")
    @Length(max= 64,message="编码长度不能超过64")
    private String bankName;

    @NotBlank(message="[开户行账号]不能为空")
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("开户行账号")
    @Length(max= 64,message="编码长度不能超过64")
    private String bankAccount;

    @NotNull(message="[状态（0-无效，1-有效）]不能为空")
    @ApiModelProperty("状态（0-无效，1-有效）")
    private Boolean status;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

}

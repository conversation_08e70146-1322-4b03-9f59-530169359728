package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 物流信息详情对象 logistic_info
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@ApiModel(value = "物流信息详情对象 logistic_info")
@TableName("logistic_info")
@Data
public class LogisticInfo implements Serializable {

    private static final long serialVersionUID = 6226814588156068304L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 物流单号
     */
    @NotNull(message = "[物流单号]不能为空")
    @ApiModelProperty(value = "物流单号", required = true)
    @Excel(name = "物流单号")
    private String number;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    @Excel(name = "事件描述")
    private String description;

    /**
     * 物流主状态
     */
    @ApiModelProperty(value = "物流主状态")
    @Excel(name = "物流主状态")
    private String mainStatus;

    /**
     * 物流子状态
     */
    @ApiModelProperty(value = "物流子状态")
    @Excel(name = "物流子状态")
    private String subStatus;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date curTime;

    /**
     * 当前节点在国家地区
     */
    @ApiModelProperty(value = "当前节点在国家地区")
    @Excel(name = "当前节点在国家地区")
    private String country;

    /**
     * 当前节点所在州、省
     */
    @ApiModelProperty(value = "当前节点所在州、省")
    @Excel(name = "当前节点所在州、省")
    private String state;

    /**
     * 当前节点所在城市
     */
    @ApiModelProperty(value = "当前节点所在城市")
    @Excel(name = "当前节点所在城市")
    private String city;

    /**
     * 当前节点所在街道
     */
    @ApiModelProperty(value = "当前节点所在街道")
    @Excel(name = "当前节点所在街道")
    private String street;

    /**
     * 当前节点所在经度
     */
    @ApiModelProperty(value = "当前节点所在经度")
    @Excel(name = "当前节点所在经度")
    private String longitude;

    /**
     * 当前节点所在纬度
     */
    @ApiModelProperty(value = "当前节点所在纬度")
    @Excel(name = "当前节点所在纬度")
    private String latitude;

    /**
     * 当前所在地点
     */
    @ApiModelProperty(value = "当前所在地点")
    @Excel(name = "当前所在地点")
    private String location;

    /**
     * 创建时间
     */
    private Date createTime;
}

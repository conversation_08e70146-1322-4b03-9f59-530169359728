package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单反馈表(模特)
 *
 * @TableName order_video_feed_back_material
 */
@TableName(value = "order_video_feed_back_material")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoFeedBackMaterial implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 2980144871303618527L;
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 视频订单id
     */
    @TableField(value = "video_id")
    @ApiModelProperty("视频订单id")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 驳回标题
     */
    @TableField(value = "title")
    @ApiModelProperty("驳回标题")
    private String title;

    /**
     * 0:待确认,1:已驳回,2:已完成
     */
    @TableField(value = "status")
    @ApiModelProperty("0:待确认,1:已驳回,2:已完成")
    private Integer status;

    /**
     * 模特是否回复被驳回的素材（0:已回复,1:未回复）
     */
    @TableField(value = "reply_status")
    @ApiModelProperty("模特是否回复被驳回的素材（0:已回复,1:未回复）")
    private Integer replyStatus;


    /**
     * 文件下载状态 (0-否， 1-是)
     */
    @TableField(value = "download_status")
    @ApiModelProperty("文件下载状态 (0-否， 1-是)")
    private Integer downloadStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}

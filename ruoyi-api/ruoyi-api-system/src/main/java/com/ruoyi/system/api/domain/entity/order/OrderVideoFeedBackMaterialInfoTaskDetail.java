package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2025-03-20 15:01:48
 */
@Data
@TableName("order_video_feed_back_material_info_task_detail")
public class OrderVideoFeedBackMaterialInfoTaskDetail implements Serializable {
    private static final long serialVersionUID = 7021337083839927860L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模特反馈素材ID
     */
    @ApiModelProperty("模特反馈素材ID")
    private Long materialInfoId;

    /**
     * 反馈给商家素材ID（FK：order_video_feed_back.id）
     */
    @ApiModelProperty("反馈给商家素材ID（FK：order_video_feed_back.id）")
    private Long feedBackId;

    /**
     * 视频订单ID（FK：order_video.id）
     */
    @ApiModelProperty(value = "视频订单ID（FK：order_video.id）")
    private Long videoId;

    /**
     * 回退ID
     */
    @ApiModelProperty(value = "回退ID")
    private Long rollbackId;

    /**
     * 任务单明细ID（FK：order_video_task_detail.id）
     */
    @ApiModelProperty(value = "任务单明细ID（FK：order_video_task_detail.id）")
    private Long taskDetailId;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitBy;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    private Long submitById;
}

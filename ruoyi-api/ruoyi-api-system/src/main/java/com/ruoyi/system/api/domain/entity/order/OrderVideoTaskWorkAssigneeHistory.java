package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/10 14:01
 */
@Data
@TableName("order_video_task_work_assignee_history")
public class OrderVideoTaskWorkAssigneeHistory implements Serializable {
    private static final long serialVersionUID = 4837213743783019886L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 工单编号
     */
    private String taskNum;

    /**
     * 原处理人
     */
    private Long originalAssigneeId;

    /**
     * 当前处理人
     */
    private Long currentAssigneeId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

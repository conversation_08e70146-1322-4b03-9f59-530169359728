package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单反馈表(商家)
 *
 * @TableName order_video_feed_back
 */
@TableName(value = "order_video_feed_back")
@Data
public class OrderVideoFeedBack implements Serializable {
    private static final long serialVersionUID = 4069738553651054727L;
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 视频订单id
     */
    @ApiModelProperty("视频订单id")
    @NotNull(message = "[视频id]不能为空")
    private Long videoId;

    /**
     * 模特反馈素材ID
     */
    @ApiModelProperty("模特反馈素材ID")
    private Long materialInfoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 反馈类型（1:视频,2:视频和照片,3:照片）
     */
    @ApiModelProperty("反馈类型（1:视频,2:视频和照片,3:照片）")
    @NotNull(message = "[反馈类型]不能为空")
    private Integer type;

    /**
     * 视频地址
     */
    @ApiModelProperty("视频地址")
    @Pattern(regexp = "^https://.*$", message = "[图片地址]格式不正确")
    @Size(max = 1000, message = "[视频地址]长度不能超过1000个字符")
    private String videoUrl;

    /**
     * 图片地址
     */
    @ApiModelProperty("图片地址")
    @Pattern(regexp = "^https://.*$", message = "[图片地址]格式不正确")
    @Size(max = 1000, message = "[图片地址]长度不能超过1000个字符")
    private String picUrl;

    /**
     * 视频评分
     */
    @ApiModelProperty("视频评分")
    private Float videoScore;

    /**
     * 评价内容
     */
    @ApiModelProperty("评价内容")
    @Size(max = 300, message = "[评价内容]长度不能超过300个字符")
    private String videoScoreContent;

    /**
     * 视频评分人
     */
    @ApiModelProperty("视频评分人")
    private String videoScoreBy;

    /**
     * 视频评分人ID
     */
    @ApiModelProperty("视频评分人ID")
    private Long videoScoreById;

    /**
     * 视频评分时间
     */
    @ApiModelProperty("视频评分时间")
    private Date videoScoreTime;

    /**
     * 是否是新的（1：是，0：不是）
     */
    @ApiModelProperty("是否是新的（1：是，0：不是）")
    private Integer isNew;

    /**
     * 修改理由
     */
    @ApiModelProperty("修改理由")
    private String modifyReason;

    /**
     * 自动确认时间
     */
    @ApiModelProperty("自动确认时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date overTime;

    /**
     * 反馈人姓名
     */
    @ApiModelProperty(value = "反馈人姓名")
    private String feedbackBy;

    /**
     * 反馈人ID
     */
    @ApiModelProperty(value = "反馈人ID")
    private Long feedbackById;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    private Date feedbackTime;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String createBy;

    /**
     * 创建用户id
     */
    @ApiModelProperty("创建用户id")
    private Long createUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;
    
}

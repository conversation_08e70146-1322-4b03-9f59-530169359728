package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单_发票对象 order_invoice_reopen
 *
 * <AUTHOR>
 * @date 2025-1-14
 */
@ApiModel(value = "订单_发票对象 order_invoice_reopen")
@TableName("order_invoice_reopen")
@Data
public class OrderInvoiceReopen implements Serializable {
    private static final long serialVersionUID = 5851367341391180945L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 旧发票ID (FK:order_invoice.id)
     */
    @ApiModelProperty(value = "旧发票ID (FK:order_invoice.id)")
    private Long oldInvoiceId;

    /**
     * 新发票ID (FK:order_invoice.id)
     */
    @ApiModelProperty(value = "新发票ID (FK:order_invoice.id)")
    private Long newInvoiceId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
}

package com.ruoyi.system.api.domain;

import com.ruoyi.system.api.domain.vo.order.OrderVideoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-12-17 18:54
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderVideoReceivableAuditDetailVO extends OrderVideoVO implements Serializable {
    private static final long serialVersionUID = -4813808782243302970L;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;
}

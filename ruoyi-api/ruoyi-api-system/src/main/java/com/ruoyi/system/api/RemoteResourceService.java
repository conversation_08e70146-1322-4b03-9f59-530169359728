
package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.factory.RemoteResourceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Collection;
import java.util.List;

/**
 * 图片视频资源表服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteResourceService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteResourceFallbackFactory.class)
public interface RemoteResourceService {


    /**
     * 新增图片视频资源
     *
     * @param bizResources 图片视频资源
     */
    @PostMapping("/biz-resource/insert-resource")
    public List<BizResource> saveBatchBizResource(@RequestBody Collection<BizResource> bizResources, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteDictDataService;
import com.ruoyi.system.api.domain.entity.SysDictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteDictDataFallbackFactory implements FallbackFactory<RemoteDictDataService> {

    @Override
    public RemoteDictDataService create(Throwable throwable)
    {
        log.error("数据字典信息服务失败");
        return new RemoteDictDataService() {
            @Override
            public List<SysDictData> selectDictDataByType(String dictType, String source) {
                return Collections.emptyList();
            }
        };
    }
}

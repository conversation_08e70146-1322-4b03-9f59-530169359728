package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:05
 */
@TableName("order_video_change_log_info")
@Data
public class OrderVideoChangeLogInfo implements Serializable {
    private static final long serialVersionUID = -3063970449039159404L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 变更记录id (FK:order_video_change_log.id)
     */
    @ApiModelProperty("变更记录id")
    private Long changeLogId;
    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    private String fieldName;
    /**
     * 字段类型
     */
    @ApiModelProperty("字段类型")
    private String fieldType;

    /**
     * 当前值
     */
    @ApiModelProperty("当前值")
    private Object value;
}

package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteTranslateService;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateBatchDTO;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateDTO;
import com.ruoyi.system.api.domain.vo.biz.translate.TranslateVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 翻译服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteTranslateFallbackFactory implements FallbackFactory<RemoteTranslateService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteTranslateFallbackFactory.class);

    @Override
    public RemoteTranslateService create(Throwable throwable) {
        log.error("翻译服务调用失败");
        return new RemoteTranslateService() {
            @Override
            public List<String> translateBatch(TranslateBatchDTO translateBatchDTO, String source) {
                log.error("批量翻译失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public TranslateVO translateStr(TranslateDTO dto, String source) {
                log.error("单句翻译失败:{}", throwable.getMessage());
                return new TranslateVO("","");
            }
        };
    }
}

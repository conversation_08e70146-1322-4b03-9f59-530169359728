package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/25 17:38
 */
@Data
@TableName("order_video_rollback_record")
public class OrderVideoRollbackRecord implements Serializable {
    private static final long serialVersionUID = -7472896936547540977L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 回退次数
     */
    @ApiModelProperty(value = "回退次数")
    private Integer count;

    /**
     * 回退原因
     */
    @ApiModelProperty(value = "回退原因")
    private String cause;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operateBy;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operateById;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date operateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

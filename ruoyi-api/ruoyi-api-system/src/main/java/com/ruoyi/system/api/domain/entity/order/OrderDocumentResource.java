package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单支付凭证关联对象 order_document_resource
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@ApiModel(value = "订单支付凭证关联对象 order_document_resource")
@TableName("order_document_resource")
@Data
public class OrderDocumentResource implements Serializable {
    private static final long serialVersionUID = -1865400340019519643L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 订单号 (FK：order_table.order_num)
     */
    @ApiModelProperty(value = "订单号 (FK：order_table.order_num)", notes = "FK：order_table.order_num", required = true)
    @NotNull(message = "[订单号 ]不能为空")
    @Excel(name = "订单号 ", readConverterExp = "FK：order_table.order_num")
    private String orderNum;

    /**
     * 支付单号
     */
    @ApiModelProperty(value = "支付单号")
    private String payNum;

    /**
     * 图片资源URI
     */
    @ApiModelProperty(value = "图片资源URI", required = true)
    @NotNull(message = "[图片资源URI]不能为空")
    @Excel(name = "图片资源URI")
    private String objectKey;

    /**
     * 0-商家上传， 1-平台上传
     */
    @NotNull(message = "[0-商家上传， 1-平台上传]不能为空")
    @ApiModelProperty(value = "0-商家上传， 1-平台上传", required = true)
    @Excel(name = "0-商家上传， 1-平台上传")
    private Integer uploadType;
}

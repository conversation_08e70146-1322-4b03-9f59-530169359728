package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 视频_购物车_关联内容 video_cart_content
 *
 * <AUTHOR>
 * @date 2024-08-01
 */
@ApiModel(value = "视频_购物车_关联内容 video_cart_content")
@TableName("video_cart_content")
@Data
public class VideoCartContent implements Serializable {

    private static final long serialVersionUID = -7625329200646296832L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 购物车id FK:video_cart.id
     */
    @NotNull(message = "[购物车id FK:video_cart.id]不能为空")
    @ApiModelProperty(value = "购物车id FK:video_cart.id", required = true)
    @Excel(name = "购物车id FK:video_cart.id")
    private Long videoCartId;

    /**
     * 内容
     */
    @NotNull(message = "[内容]不能为空")
    @ApiModelProperty(value = "内容", required = true)
    @Excel(name = "内容")
    private String content;

    /**
     * 排序
     */
    @NotNull(message = "[排序]不能为空")
    @ApiModelProperty(value = "排序", required = true)
    @Excel(name = "排序")
    private Integer sort;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}

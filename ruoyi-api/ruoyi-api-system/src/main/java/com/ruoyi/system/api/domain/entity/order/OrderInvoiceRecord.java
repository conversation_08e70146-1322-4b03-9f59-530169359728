package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:14
 */
@Data
@TableName("order_invoice_record")
public class OrderInvoiceRecord implements Serializable {
    private static final long serialVersionUID = -82034291969184563L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票ID (FK:order_invoice.id)
     */
    @ApiModelProperty(value = "发票ID")
    private Long invoiceId;

    /**
     * 类型（1：发票，2：红冲）
     */
    @ApiModelProperty(value = "类型（1：发票，2：红冲）")
    private Integer type;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 发票号 or 红冲票号
     */
    @ApiModelProperty(value = "发票号 or 红冲票号")
    private String number;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    private Date invoicingTime;

    /**
     * 发票URI
     */
    @ApiModelProperty(value = "发票URI")
    private String objectKey;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 图片URI
     */
    @ApiModelProperty(value = "发票文件URI")
    @TableField(exist = false)
    private List<String> objectKeys;

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
        if (StrUtil.isNotBlank(objectKey)) {
            this.objectKeys = StrUtil.split(objectKey, StrUtil.COMMA);
        }
    }
}

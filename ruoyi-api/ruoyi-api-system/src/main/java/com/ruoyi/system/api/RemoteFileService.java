package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.vo.FileUploadLinkVo;
import com.ruoyi.system.api.factory.RemoteFileFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;


/**
 * 文件服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteFileService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteFileFallbackFactory.class)
public interface RemoteFileService {
    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResource upload(@RequestPart(value = "file") MultipartFile file);

    @PostMapping(value = "/upload/link/proxy")
    String uploadFileByLink(@RequestBody FileUploadLinkVo fileUploadLinkVo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    default String uploadFileByLink(@RequestBody FileUploadLinkVo fileUploadLinkVo){
        return uploadFileByLink(fileUploadLinkVo, SecurityConstants.INNER);
    }

}

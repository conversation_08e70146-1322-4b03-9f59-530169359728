package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 物流信息对象 logistic
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@ApiModel(value = "物流信息对象 logistic")
@TableName("logistic")
@Data
public class Logistic implements Serializable {

    private static final long serialVersionUID = -3599378257477161573L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 物流跟踪状态（TRACKING_UPDATED:持续更新,TRACKING_STOPPED:停止跟踪）
     */
    @ApiModelProperty(value = "物流跟踪状态（TRACKING_UPDATED:持续更新,TRACKING_STOPPED:停止跟踪）")
    @Excel(name = "物流跟踪状态")
    private String event;

    /**
     * 物流单号
     */
    @NotNull(message = "[物流单号]不能为空")
    @ApiModelProperty(value = "物流单号")
    @Excel(name = "物流单号")
    private String number;

    /**
     * 物流最新主状态
     */
    @ApiModelProperty(value = "物流最新主状态")
    private String latestMainStatus;

    /**
     * 物流最新子状态
     */
    @ApiModelProperty(value = "物流最新子状态")
    private String latestSubStatus;

    /**
     * 运输商名称
     */
    @ApiModelProperty(value = "运输商名称")
    @Excel(name = "运输商名称")
    private String carrier;

    /**
     * 运输商代码
     */
    @ApiModelProperty(value = "运输商代码")
    @Excel(name = "运输商代码")
    private String carrierCode;

    /**
     * 运输商别名
     */
    @ApiModelProperty(value = "运输商别名")
    @Excel(name = "运输商别名")
    private String carrierAlias;

    /**
     * 运输商联系电话
     */
    @ApiModelProperty(value = "运输商联系电话")
    @Excel(name = "运输商联系电话")
    private String carrierTel;

    /**
     * 运输商所属国家
     */
    @ApiModelProperty(value = "运输商所属国家")
    @Excel(name = "运输商所属国家")
    private String carrierCountry;

    /**
     * 运输商官网
     */
    @ApiModelProperty(value = "运输商官网")
    @Excel(name = "运输商官网")
    private String carrierHomepage;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;


    /**
     * 发件地址_国家或地区（大写）
     */
    @ApiModelProperty(value = "发件地址_国家或地区（大写）")
    @Excel(name = "发件地址_国家或地区（大写）")
    private String saCountry;

    /**
     * 发件地址_州、省
     */
    @ApiModelProperty(value = "发件地址_州、省")
    @Excel(name = "发件地址_州、省")
    private String saState;

    /**
     * 发件地址_城市
     */
    @ApiModelProperty(value = "发件地址_城市")
    @Excel(name = "发件地址_城市")
    private String saCity;

    /**
     * 发件地址_街道
     */
    @ApiModelProperty(value = "发件地址_街道")
    @Excel(name = "发件地址_街道")
    private String saStreet;

    /**
     * 发件地址_邮编
     */
    @ApiModelProperty(value = "发件地址_邮编")
    @Excel(name = "发件地址_邮编")
    private String saPostalCode;

    /**
     * 发件地址_经度
     */
    @ApiModelProperty(value = "发件地址_经度")
    @Excel(name = "发件地址_经度")
    private String saLongitude;

    /**
     * 发件地址_纬度
     */
    @ApiModelProperty(value = "发件地址_纬度")
    @Excel(name = "发件地址_纬度")
    private String saLatitude;

    /**
     * 收件地址_国家或地区（大写）
     */
    @ApiModelProperty(value = "收件地址_国家或地区（大写）")
    @Excel(name = "收件地址_国家或地区（大写）")
    private String raCountry;

    /**
     * 收件地址_州、省
     */
    @ApiModelProperty(value = "收件地址_州、省")
    @Excel(name = "收件地址_州、省")
    private String raState;

    /**
     * 收件地址_城市
     */
    @ApiModelProperty(value = "收件地址_城市")
    @Excel(name = "收件地址_城市")
    private String raCity;

    /**
     * 收件地址_街道
     */
    @ApiModelProperty(value = "收件地址_街道")
    @Excel(name = "收件地址_街道")
    private String raStreet;

    /**
     * 收件地址_邮编
     */
    @ApiModelProperty(value = "收件地址_邮编")
    @Excel(name = "收件地址_邮编")
    private String raPostalCode;

    /**
     * 收件地址_经度
     */
    @ApiModelProperty(value = "收件地址_经度")
    @Excel(name = "收件地址_经度")
    private String raLongitude;

    /**
     * 收件地址_纬度
     */
    @ApiModelProperty(value = "收件地址_纬度")
    @Excel(name = "收件地址_纬度")
    private String raLatitude;
}

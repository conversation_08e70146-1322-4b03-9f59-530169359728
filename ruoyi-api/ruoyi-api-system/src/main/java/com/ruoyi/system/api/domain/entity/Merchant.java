package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 商家信息对象 merchant
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@ApiModel(value = "商家信息对象 merchant")
@Data
public class Merchant implements Serializable {

    private static final long serialVersionUID = 7074665876753891469L;
    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID")
    @Excel(name = "商家ID")
    private Long merchantId;

    /**
     * 商家账号
     */
    @NotNull(message = "[商家账号]不能为空")
    @ApiModelProperty(value = "商家账号", required = true)
    private String merchantName;

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码")
    @Excel(name = "商家编码")
    private String merchantCode;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    @Excel(name = "手机号码")
    private String phonenumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    @ApiModelProperty(value = "用户性别（0男 1女 2未知）")
    @Excel(name = "用户性别")
    private String sex;

    /**
     * 数据范围（1：全部数据权限  4：本部门及以下数据权限 5:仅本人数据）
     */
    @ApiModelProperty(value = "数据范围（1：全部数据权限  4：本部门及以下数据权限 5:仅本人数据）")
    @Excel(name = "数据范围")
    private String dataScope;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址")
    @Excel(name = "头像地址")
    private String avatar;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @Excel(name = "密码")
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    @ApiModelProperty(value = "帐号状态（0正常 1停用）")
    @Excel(name = "帐号状态（0正常 1停用）")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    @Excel(name = "删除标志")
    private String delFlag;

    /**
     * 最后登录IP
     */
    @ApiModelProperty(value = "最后登录IP")
    @Excel(name = "最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date loginDate;


    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updateBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 32, message = "备注不能超过32个字符")
    private String remark;
}

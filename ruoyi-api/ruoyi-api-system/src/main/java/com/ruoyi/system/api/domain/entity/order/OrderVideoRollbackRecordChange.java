package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/25 17:38
 */
@Data
@TableName("order_video_rollback_record_change")
public class OrderVideoRollbackRecordChange implements Serializable {
    private static final long serialVersionUID = 2436731999862215129L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty(value = "回退ID")
    private Long rollbackId;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    private Long shootModelId;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    private Long issueId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "排单类型（1:排单,2:携带排单）")
    private Integer scheduleType;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "超额说明")
    private String overstatement;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 主携带数量
     */
    @ApiModelProperty(value = "主携带数量")
    private Integer mainCarryCount;

    /**
     * 主携带视频订单id (FK:order_video.id)
     */
    @ApiModelProperty(value = "主携带视频订单id (FK:order_video.id)")
    private Long mainCarryVideoId;

    /**
     * 标记物流状态（1:标记发货）
     */
    @ApiModelProperty(value = "标记物流状态（1:标记发货）")
    private Integer logisticFlag;

    /**
     * 订单进入待确认的时间
     */
    @ApiModelProperty(value = "订单进入待确认的时间")
    private Date unConfirmTime;

    /**
     * 订单进入待完成的时间
     */
    @ApiModelProperty(value = "订单进入待完成的时间")
    private Date unFinishedTime;

    /**
     * 订单进入需确认的时间
     */
    @ApiModelProperty(value = "订单进入需确认的时间")
    private Date needConfirmTime;

    /**
     * 视频订单自动完成时间
     */
    @ApiModelProperty(value = "视频订单自动完成时间")
    private Date autoCompleteTime;

    /**
     * 最新提交模特时间
     */
    @ApiModelProperty(value = "最新提交模特时间")
    private Date lastModelSubmitTime;

    /**
     * 订单释放时间（商家无意向模特且首次到达待匹配状态、首个意向模特不想要，以上两种情况会设置此值）
     */
    @ApiModelProperty(value = "订单释放时间")
    private Date releaseTime;

    /**
     * 订单完全释放flag（1:表示视频订单不需要再过滤24小时，可以放到模特订单公池中）
     */
    @ApiModelProperty(value = "订单完全释放flag")
    private Integer releaseFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

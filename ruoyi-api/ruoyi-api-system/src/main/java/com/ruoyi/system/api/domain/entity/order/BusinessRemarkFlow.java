package com.ruoyi.system.api.domain.entity.order;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 商家备注流水
 *
 * <AUTHOR>
 * @TableName business_remark_flow
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class BusinessRemarkFlow implements Serializable {


    private static final long serialVersionUID = -6969307536780693855L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    @Size(max = 1000, message = "编码长度不能超过1000")
    @ApiModelProperty("备注")
    private String remark;

    @NotNull(message = "[创建人id（sys_user.user_id）]不能为空")
    @ApiModelProperty("创建人id（sys_user.user_id）")
    private Long createById;

    @NotBlank(message = "[创建人名称（sys_user.user_name）]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("创建人名称（sys_user.user_name）")
    private String createBy;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date creatTime;
}

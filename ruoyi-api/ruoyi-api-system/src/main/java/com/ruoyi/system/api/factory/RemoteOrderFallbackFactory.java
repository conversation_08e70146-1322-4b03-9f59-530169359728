package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteOrderService;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.logistic.LogisticFollowNotifyDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.ModelUpdateAddressDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.ModelBasicDataVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.order.AddPreselectRemoteVO;
import com.ruoyi.system.api.domain.vo.order.ModelOrderVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelTimeoutVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 订单服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteOrderFallbackFactory implements FallbackFactory<RemoteOrderService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteOrderFallbackFactory.class);

    @Override
    public RemoteOrderService create(Throwable throwable) {
        log.error("订单服务调用失败");
        return new RemoteOrderService() {

            @Override
            public List<Long> queryOrderModel(Collection<Long> modelId, String source) {
                log.error("查询有逾期未反馈素材和无法接单的模特失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public OrderVideoStatisticsVO orderVideoStatistics(OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
                log.error("获取订单统计失败:{}", throwable.getMessage());
                OrderVideoStatisticsVO vo = new OrderVideoStatisticsVO();
                vo.setOrderVideoTotal(0);
                vo.setRecentOrderTotal(0);
                vo.setPreFinishOrderTotal(0);
                return vo;
            }

            @Override
            public List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
                log.error("获取订单统计详情失败:{}", throwable.getMessage());
                return List.of();
            }

            @Override
            public Boolean updateOrderVideoContact(UpdateOrderContactDTO dto, String source) {
                log.error("批量更新视频订单的对接人失败:{}", throwable.getMessage());
                return false;
            }

            @Override
            public void updateBatchOrderVideoProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto, String source) {
                log.error("接收抓取亚马逊图片更新视频订单失败:{}", throwable.getMessage());
            }

            @Override
            public void updateBatchOrderCartProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto, String source) {
                log.error("接收抓取亚马逊图片更新购物车订单失败:{}", throwable.getMessage());
            }

            @Override
            public List<ModelOrderVO> getModelOrderCount(Collection<Long> modelIds, String source) {
                log.error("获取模特待拍数、已完成订单数、超时订单失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<OrderModelTimeoutVO> getModelOvertimeRateAndAfterSaleRate(String source) {
                log.error("获取模特超时率、售后率失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public Long getValidOrderCount(Long bizUserId, String source) {
                log.error("获取订单有效数据失败:{}", throwable.getMessage());
                return 0L;
            }

            @Override
            public String savePromotionActivityAmendmentRecord(PromotionActivityAmendmentRecord promotionActivityAmendmentRecord, String source) {
                log.error("保存渠道配置日志信息:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Long getUnCancelOrderCount(Long bizUserId, String source) {
                log.error("获取未取消订单数量:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Boolean outPreselectModel(List<OutPreselectModelDTO> dtoList, String source) {
                log.error("更新预选模特列表为已淘汰失败:{}", throwable.getMessage());
                return false;
            }

            @Override
            public Set<Long> selectNormalPreselectModelByMatchId(Long matchId, Long videoId, String source) {
                log.error("添加预选模特列表查询匹配单下非淘汰的模特失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public List<OrderPayDetailVO> getBasePayDetailVOS(OrderPayDetailDTO dto, String source) {
                log.error("获取订单明细基础数据失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public String saveOrderPayLog(OrderPayLogDTO dto, String source) {
                log.error("保存订单流水数据失败，请求参数{}", dto.toString());
                log.error("保存订单流水数据失败：{}", throwable.getMessage());
                return null;
            }

            @Override
            public String banMemberInvoice(Long businessId, String source) {
                log.error("禁用发票数据失败，请求参数{}", businessId);
                log.error("禁用发票数据失败：{}", throwable.getMessage());
                return null;
            }

            @Override
            public String saveOrderPayeeAccount(OrderPayAccountDTO dto, String source) {
                log.error("保存订单收款账号数据失败，请求参数{}", dto.toString());
                log.error("保存订单收款账号数据失败：{}", throwable.getMessage());
                return null;
            }

            @Override
            public List<OrderPayeeAccount> queryOrderPayeeAccountListByOrderNums(Collection<String> orderNums, String source) {
                log.error("获取收款账号列表数据失败，请求参数{}", orderNums);
                log.error("获取收款账号列表数据失败：{}", throwable.getMessage());
                return List.of();
            }

            @Override
            public String updateIssueId(UpdateIssueIdDTO dto, String source) {
                log.error("模特变更对接客服更新视频订单的出单人ID失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public String clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO dto, String source) {
                log.error("清楚购物车意向模特失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Boolean withdrawalSuccess(List<WithdrawalSuccessDTO> dtoList, String source) {
                return false;
            }

            @Override
            public List<OrderVideoRefund> getOrderVideoRefundList(List<String> numbers, String inner) {
                return Collections.emptyList();
            }

            @Override
            public List<Order> getPayedOrderList(String inner) {
                return Collections.emptyList();
            }

            @Override
            public List<Order> getPayedUnCheckOrderList(String inner) {
                return Collections.emptyList();
            }

            @Override
            public String closeAllOrder(Collection<String> orderNums, String source) {
                log.error("关闭订单失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public List<Order> getOrderListByOrderNums(Collection<String> orderNums, String source) {
                log.error("查询订单失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public String logisticFollowNotify(LogisticFollowNotifyDTO dto, String source) {
                log.error("通知失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public String modelUpdateAddress(ModelUpdateAddressDTO dto, String source) {
                log.error("模特变更通知失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public AddPreselectRemoteVO selectRejectModelIdByVideoId(Long videoId, String inner) {
                return null;
            }

            @Override
            public Boolean hasValidVideoOrderByBusinessId(Long businessId, String startTime, String inner) {
                return null;
            }

            @Override
            public List<OrderMember> getValidOrderMemberList(Long bizUserId, String source) {
                return null;
            }

            @Override
            public ModelBasicDataVO getModelBasicData(String source) {
                return null;
            }

            @Override
            public List<ModelOrderRankingInfo> getModelOrderRanking(String date, String source) {
                return null;
            }

            @Override
            public Map<String, List<ModelOrderRankingInfo>> getModelOrderRankings(List<String> collect, String source) {
                return Map.of();
            }

            @Override
            public Set<Long> selectPreselectModelIdsByMatchId(Long matchId, String source) {
                return null;
            }

            @Override
            public List<PieChartVO> getModelOrderScheduledData(Collection<Long> modelIds, String source) {
                return null;
            }

            @Override
            public Date getOrderFirstMatchTime(Long orderId, String source) {
                log.error("调用{}异常:{}", "getOrderFirstMatchTime", throwable.getMessage());
                return new Date(0L);
            }

            @Override
            public List<ModelDataTableListVO> selectModelDataTableListByCondition(ModelDataTableListDTO modelDataTableListDTO, String source) {
                return null;
            }

            @Override
            public List<Long> selectSameProductRollbackModelIdsByVideoId(Long videoId, String source) {
                return null;
            }
        };
    }
}

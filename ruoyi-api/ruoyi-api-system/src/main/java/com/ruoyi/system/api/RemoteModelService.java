
package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.model.CannotAcceptModelDTO;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.biz.model.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.ruoyi.system.api.factory.RemoteModelFallbackFactory;
import com.ruoyi.system.api.model.LoginModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 模特服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteModelService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteModelFallbackFactory.class)
public interface RemoteModelService {

    /**
     * 查询模特信息列表（内部请求）
     */
    @PostMapping("/model/inner-list")
    public List<ModelInfoVO> innerList(@RequestBody ModelListDTO modelListDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 查询不可接单的模特（内部请求）
     * @return 不可接单的模特
     */
    @PostMapping("/model/query-cannot-accept-list")
    public List<Model> queryCannotAcceptList(@RequestBody CannotAcceptModelDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询模特关联运营
     */
    @PostMapping("/model/query-model-person")
    public List<ModelPerson> queryModelPerson(@RequestBody Collection<Long> modelIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据模特账号获取模特数据
     * @param account  模特账号
     * @return
     */
    @GetMapping("/account/getModelByModelLoginAccount/{account}")
    public LoginModel getModelByModelLoginAccount(@PathVariable("account") String account);

    /**
     * 更新模特最新登录时间
     * @param account
     * @return
     */
    @GetMapping("/account/modelLoginForAccount")
    public String modelLoginForAccount(@RequestParam("account") Long account);


    /**
     * 模糊查询模特信息列表（模特名称、模特账号）
     */
    @PostMapping("/model/query-like-model-list")
    public List<ModelInfoVO> queryLikeModelList(@RequestBody ModelListDTO modelListDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 查询模特简单信息（用于订单列表模特数据）
     */
    @PostMapping("/model/query-model-simple-list")
    public List<ModelOrderSimpleVO> queryModelSimpleList(@RequestBody ModelListDTO modelListDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 查询模特信息 用于记录视频订单模特变更记录
     */
    @PostMapping("/model/select-model-change-list")
    public List<ModelChangeVO> selectModelChangeList(@RequestBody ModelListDTO modelListDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询模特信息（预选模特对象）
     */
    @PostMapping("/model/select-model-info-of-preselection")
    List<AddPreselectModelListVO> selectModelInfoOfPreselection(@RequestBody ModelListDTO modelListDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询运营关联的模特
     */
    @PostMapping("/model/select-model-person-by-user-ids")
    List<ModelPerson> selectModelPersonByUserIds(@RequestBody Collection<Long> userIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取英文部客服 新增/淘汰模特数
     */
    @GetMapping("/model/get-english-customer-service-added-oust-model-counts")
    List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedOustModelCounts(@RequestParam("date") String date, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 客服数据-获取英文部关联模特数据
     */
    @GetMapping("/model/select-english-customer-service--model-data")
    List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceModelData(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询模特下拉框
     */
    @PostMapping("/model/model-select")
    List<ModelSelectVO> modelSelect(@RequestBody ModelListDTO dto);
}

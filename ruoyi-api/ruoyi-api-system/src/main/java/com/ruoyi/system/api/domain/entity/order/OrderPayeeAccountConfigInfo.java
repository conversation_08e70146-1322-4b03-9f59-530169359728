package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收款人账号配置表
 * @TableName order_payee_account_config_info
 */
@TableName(value ="order_payee_account_config_info")
@Data
public class OrderPayeeAccountConfigInfo implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 收款信息id
     */
    @TableField(value = "payee_id")
    private Long payeeId;

    /**
     * 状态 0-弃用 1-使用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * type 类型
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 主体名称
     */
    @TableField(value = "account_name")
    private String accountName  ;

    /**
     * 收款账号名称/银行账号
     */
    @TableField(value = "bank_account")
    private String bankAccount;

    /**
     * 开户行名称/银行所在地
     */
    @TableField(value = "bank_name")
    private String bankName;

    /**
     * 收款账号类型
     */
    @TableField(value = "company_account_type")
    private String companyAccountType;

    /**
     * 银行代码
     */
    @TableField(value = "company_bank_code")
    private String companyBankCode;

    /**
     * 分行代码
     */
    @TableField(value = "company_bank_sub_code")
    private String companyBankSubCode;

    /**
     * 分行代码
     */
    @TableField(value = "company_bank_swift_code")
    private String companyBankSwiftCode;

    /**
     * 银行名称
     */
    @TableField(value = "company_bank_name")
    private String companyBankName;

    /**
     * 银行地址
     */
    @TableField(value = "company_bank_address")
    private String companyBankAddress;

    /**
     * 收款人地址
     */
    @TableField(value = "company_bank_payee_address")
    private String companyBankPayeeAddress;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by_id")
    private Long createById;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 更新人ID
     */
    @TableField(value = "update_by_id")
    private Long updateById;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderPayeeAccountConfigInfo other = (OrderPayeeAccountConfigInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPayeeId() == null ? other.getPayeeId() == null : this.getPayeeId().equals(other.getPayeeId()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getBankAccount() == null ? other.getBankAccount() == null : this.getBankAccount().equals(other.getBankAccount()))
            && (this.getBankName() == null ? other.getBankName() == null : this.getBankName().equals(other.getBankName()))
            && (this.getCompanyAccountType() == null ? other.getCompanyAccountType() == null : this.getCompanyAccountType().equals(other.getCompanyAccountType()))
            && (this.getCompanyBankCode() == null ? other.getCompanyBankCode() == null : this.getCompanyBankCode().equals(other.getCompanyBankCode()))
            && (this.getCompanyBankSubCode() == null ? other.getCompanyBankSubCode() == null : this.getCompanyBankSubCode().equals(other.getCompanyBankSubCode()))
            && (this.getCompanyBankSwiftCode() == null ? other.getCompanyBankSwiftCode() == null : this.getCompanyBankSwiftCode().equals(other.getCompanyBankSwiftCode()))
            && (this.getCompanyBankPayeeAddress() == null ? other.getCompanyBankPayeeAddress() == null : this.getCompanyBankPayeeAddress().equals(other.getCompanyBankPayeeAddress()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getCreateById() == null ? other.getCreateById() == null : this.getCreateById().equals(other.getCreateById()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getUpdateById() == null ? other.getUpdateById() == null : this.getUpdateById().equals(other.getUpdateById()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPayeeId() == null) ? 0 : getPayeeId().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getBankAccount() == null) ? 0 : getBankAccount().hashCode());
        result = prime * result + ((getBankName() == null) ? 0 : getBankName().hashCode());
        result = prime * result + ((getCompanyAccountType() == null) ? 0 : getCompanyAccountType().hashCode());
        result = prime * result + ((getCompanyBankCode() == null) ? 0 : getCompanyBankCode().hashCode());
        result = prime * result + ((getCompanyBankSubCode() == null) ? 0 : getCompanyBankSubCode().hashCode());
        result = prime * result + ((getCompanyBankSwiftCode() == null) ? 0 : getCompanyBankSwiftCode().hashCode());
        result = prime * result + ((getCompanyBankPayeeAddress() == null) ? 0 : getCompanyBankPayeeAddress().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateById() == null) ? 0 : getCreateById().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateById() == null) ? 0 : getUpdateById().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", payeeId=").append(payeeId);
        sb.append(", status=").append(status);
        sb.append(", bankAccount=").append(bankAccount);
        sb.append(", bankName=").append(bankName);
        sb.append(", companyAccountType=").append(companyAccountType);
        sb.append(", companyBankCode=").append(companyBankCode);
        sb.append(", companyBankSubCode=").append(companyBankSubCode);
        sb.append(", companyBankSwiftCode=").append(companyBankSwiftCode);
        sb.append(", companyBankPayeeAddress=").append(companyBankPayeeAddress);
        sb.append(", createBy=").append(createBy);
        sb.append(", createById=").append(createById);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateById=").append(updateById);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
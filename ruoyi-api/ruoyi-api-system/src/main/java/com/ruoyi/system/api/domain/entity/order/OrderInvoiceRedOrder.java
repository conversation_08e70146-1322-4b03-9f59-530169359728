package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:07
 */
@Data
@TableName("order_invoice_red_order")
public class OrderInvoiceRedOrder implements Serializable {
    private static final long serialVersionUID = -2917293032121436537L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票红冲ID (FK:order_invoice_red.id)
     */
    @ApiModelProperty(value = "发票红冲ID")
    private Long invoiceRedId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

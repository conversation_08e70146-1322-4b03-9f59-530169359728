package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单基础价格配置表_更改记录
 * @TableName order_base_price_config_changelog
 */
@TableName(value ="order_base_price_config_changelog")
@Data
public class OrderBasePriceConfigChangelog implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 价格类型（1-服务费）
     */
    @TableField(value = "price_type")
    private Integer priceType;

    /**
     * 现原金额(展示使用-非代理)
     */
    @TableField(value = "origin_price")
    private BigDecimal originPrice;

    /**
     * 前原金额(展示使用-非代理)
     */
    @TableField(value = "origin_price_previous")
    private BigDecimal originPricePrevious;

    /**
     * 原金额(展示使用-代理)
     */
    @TableField(value = "origin_price_proxy")
    private BigDecimal originPriceProxy;

    /**
     * 前原金额(展示使用-代理)
     */
    @TableField(value = "origin_price_proxy_previous")
    private BigDecimal originPriceProxyPrevious;

    /**
     * 现金额
     */
    @TableField(value = "current_price")
    private BigDecimal currentPrice;

    /**
     * 前现金额
     */
    @TableField(value = "current_price_previous")
    private BigDecimal currentPricePrevious;

    /**
     * 现代理现金额
     */
    @TableField(value = "current_price_proxy")
    private BigDecimal currentPriceProxy;

    /**
     * 前现代理现金额
     */
    @TableField(value = "current_price_proxy_previous")
    private BigDecimal currentPriceProxyPrevious;

    /**
     * 生效时间
     */
    @TableField(value = "since_time")
    private Date sinceTime;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by_id")
    private Long createById;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 更新人ID
     */
    @TableField(value = "update_by_id")
    private Long updateById;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderBasePriceConfigChangelog other = (OrderBasePriceConfigChangelog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getPriceType() == null ? other.getPriceType() == null : this.getPriceType().equals(other.getPriceType()))
                && (this.getOriginPrice() == null ? other.getOriginPrice() == null : this.getOriginPrice().equals(other.getOriginPrice()))
                && (this.getOriginPricePrevious() == null ? other.getOriginPricePrevious() == null : this.getOriginPricePrevious().equals(other.getOriginPricePrevious()))
                && (this.getOriginPriceProxy() == null ? other.getOriginPriceProxy() == null : this.getOriginPriceProxy().equals(other.getOriginPriceProxy()))
                && (this.getOriginPriceProxyPrevious() == null ? other.getOriginPriceProxyPrevious() == null : this.getOriginPriceProxyPrevious().equals(other.getOriginPriceProxyPrevious()))
                && (this.getCurrentPrice() == null ? other.getCurrentPrice() == null : this.getCurrentPrice().equals(other.getCurrentPrice()))
                && (this.getCurrentPricePrevious() == null ? other.getCurrentPricePrevious() == null : this.getCurrentPricePrevious().equals(other.getCurrentPricePrevious()))
                && (this.getCurrentPriceProxy() == null ? other.getCurrentPriceProxy() == null : this.getCurrentPriceProxy().equals(other.getCurrentPriceProxy()))
                && (this.getCurrentPriceProxyPrevious() == null ? other.getCurrentPriceProxyPrevious() == null : this.getCurrentPriceProxyPrevious().equals(other.getCurrentPriceProxyPrevious()))
                && (this.getSinceTime() == null ? other.getSinceTime() == null : this.getSinceTime().equals(other.getSinceTime()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateById() == null ? other.getCreateById() == null : this.getCreateById().equals(other.getCreateById()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateById() == null ? other.getUpdateById() == null : this.getUpdateById().equals(other.getUpdateById()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPriceType() == null) ? 0 : getPriceType().hashCode());
        result = prime * result + ((getOriginPrice() == null) ? 0 : getOriginPrice().hashCode());
        result = prime * result + ((getOriginPricePrevious() == null) ? 0 : getOriginPricePrevious().hashCode());
        result = prime * result + ((getOriginPriceProxy() == null) ? 0 : getOriginPriceProxy().hashCode());
        result = prime * result + ((getOriginPriceProxyPrevious() == null) ? 0 : getOriginPriceProxyPrevious().hashCode());
        result = prime * result + ((getCurrentPrice() == null) ? 0 : getCurrentPrice().hashCode());
        result = prime * result + ((getCurrentPricePrevious() == null) ? 0 : getCurrentPricePrevious().hashCode());
        result = prime * result + ((getCurrentPriceProxy() == null) ? 0 : getCurrentPriceProxy().hashCode());
        result = prime * result + ((getCurrentPriceProxyPrevious() == null) ? 0 : getCurrentPriceProxyPrevious().hashCode());
        result = prime * result + ((getSinceTime() == null) ? 0 : getSinceTime().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateById() == null) ? 0 : getCreateById().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateById() == null) ? 0 : getUpdateById().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", priceType=").append(priceType);
        sb.append(", originPrice=").append(originPrice);
        sb.append(", originPricePrevious=").append(originPricePrevious);
        sb.append(", originPriceProxy=").append(originPriceProxy);
        sb.append(", originPriceProxyPrevious=").append(originPriceProxyPrevious);
        sb.append(", currentPrice=").append(currentPrice);
        sb.append(", currentPricePrevious=").append(currentPricePrevious);
        sb.append(", currentPriceProxy=").append(currentPriceProxy);
        sb.append(", currentPriceProxyPrevious=").append(currentPriceProxyPrevious);
        sb.append(", sinceTime=").append(sinceTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", createById=").append(createById);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateById=").append(updateById);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@TableName("order_video_task_detail")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoTaskDetail implements Serializable {
    private static final long serialVersionUID = -120286963124906270L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 任务单ID(FK:order_video_task.id)
     */
    @ApiModelProperty(value = "任务单ID")
    private Long taskId;

    /**
     * 回退ID
     */
    @ApiModelProperty(value = "回退ID")
    private Long rollbackId;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String taskNum;

    /**
     * 售后分类（1：视频，2：照片）
     */
    @ApiModelProperty(value = "售后分类（1：视频，2：照片）")
    private Integer afterSaleClass;

    /**
     * 售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）
     */
    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）")
    private Integer afterSaleVideoType;

    /**
     * 售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）
     */
    @ApiModelProperty(value = "售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）")
    private Integer afterSalePicType;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private Integer workOrderType;

    /**
     * 问题描述
     */
    @ApiModelProperty(value = "问题描述")
    private String content;

    /**
     * 问题描述_英文
     */
    @ApiModelProperty(value = "问题描述_英文")
    private String contentEnglish;

    /**
     * 补充剪辑要求
     */
    @ApiModelProperty(value = "补充剪辑要求")
    private String clipRecord;

    /**
     * 问题图片（FK:order_resource.id）
     */
    @ApiModelProperty(value = "问题图片")
    @TableField("issue_pic")
    private String issuePicId;

    /**
     * 优先级（1:紧急,2:一般）
     */
    @ApiModelProperty(value = "优先级（1:紧急,2:一般）")
    private Integer priority;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitBy;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    private Long submitById;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 处理人ID
     */
    @ApiModelProperty(value = "处理人ID")
    private Long assigneeId;

    /**
     * 最新回复时间
     */
    @ApiModelProperty(value = "最新回复时间")
    private Date lastReplyTime;

    /**
     * 状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）
     */
    @ApiModelProperty(value = "状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）")
    private Integer status;

    /**
     * 关闭/完成时间
     */
    @ApiModelProperty(value = "关闭/完成时间")
    private Date endTime;

    @ApiModelProperty(value = "确认售后时间")
    private Date confirmTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

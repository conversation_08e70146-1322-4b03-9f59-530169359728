package com.ruoyi.system.api.domain.entity.order;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 微信订单表
 *
 * <AUTHOR>
 * @TableName wechat_order_table
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatOrderTable implements Serializable {

    private static final long serialVersionUID = 93383286274572213L;
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("微信应用id")
    private String appId;

    @NotBlank(message = "[商户订单号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("商户订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String mchntOrderNo;

    @NotBlank(message = "[内部订单号]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("内部订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNum;

    @NotBlank(message = "[二维码]不能为空")
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("二维码")
    @Length(max = 100, message = "编码长度不能超过100")
    private String qrcode;

    @NotNull(message = "[订单金额]不能为空")
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("是否有效（1-有效， 0-无效）")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date createTime;

}

package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteConfigService;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.dto.system.EditMemberDiscountDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteConfigFallbackFactory implements FallbackFactory<RemoteConfigService>
{
    @Override
    public RemoteConfigService create(Throwable throwable)
    {
        log.error("用户服务调用失败");
        return new RemoteConfigService() {
            @Override
            public String getConfigKey(String configKey, String source) {
                log.error("获取配置信息失败:{}", throwable.getLocalizedMessage());
                return "";
            }
//
//            @Override
//            public String editFissionMemberDiscount(EditMemberDiscountDTO dto, String source) {
//                log.error("修改配置失败:{}", throwable.getLocalizedMessage());
//                return "";
//            }

            @Override
            public String editFissionMemberDiscountV1(EditFissionChannelDiscountDTO dto, String source) {
                log.error("修改配置失败:{}", throwable.getLocalizedMessage());
                return null;
            }

            @Override
            public String editMemberDiscount(EditMemberDiscountDTO dto, String source) {
                log.error("修改分销会员活动失败:{}", throwable.getLocalizedMessage());
                return null;
            }
        };
    }
}

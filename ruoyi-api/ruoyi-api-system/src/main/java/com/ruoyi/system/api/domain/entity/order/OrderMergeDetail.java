package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-03-04 09:59:47
 */
@Data
@TableName("order_merge_detail")
public class OrderMergeDetail implements Serializable {
    private static final long serialVersionUID = 5974873620057153643L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 合并ID(FK:order_merge.id)
     */
    @ApiModelProperty(value = "合并ID")
    private Long mergeId;

    /**
     * 订单号(FK:order_table.order_num)
     */
    @ApiModelProperty(value = "订单ID")
    private String orderNum;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

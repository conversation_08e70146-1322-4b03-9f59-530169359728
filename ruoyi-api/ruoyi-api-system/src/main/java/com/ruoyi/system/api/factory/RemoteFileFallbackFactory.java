package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.dto.GeneratePdfDTO;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.vo.FileUploadLinkVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteFileFallbackFactory implements FallbackFactory<RemoteFileService> {

    @Override
    public RemoteFileService create(Throwable throwable) {
        log.error("文件服务调用失败");
        return new RemoteFileService() {
            @Override
            public BizResource upload(MultipartFile file) {
                log.error("上传文件失败:{}", throwable.getLocalizedMessage());
                return null;
            }


            @Override
            public String uploadFileByLink(FileUploadLinkVo fileUploadLinkVo, String source) {
                log.warn("根据链接上传图片失败");
                return "";
            }
        };
    }
}

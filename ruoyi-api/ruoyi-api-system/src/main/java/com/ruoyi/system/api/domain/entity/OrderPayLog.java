package com.ruoyi.system.api.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单支付记录表
 *
 * <AUTHOR>
 * @TableName order_pay_log
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderPayLog implements Serializable {

    private static final long serialVersionUID = -5417499674910935494L;
    /**
     * 主键
     */
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 订单号
     */
    @NotBlank(message = "[订单号]不能为空")
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("订单号")
    @Length(max = 32, message = "编码长度不能超过32")
    private String orderNum;

    @ApiModelProperty(value = "支付单号")
    private String payNum;
    /**
     * 订单类型（0-视频订单，1-会员订单，3-线下钱包充值订单，5.线上钱包充值）
     */
    @NotNull(message = "[订单类型（0-视频订单，1-会员订单，3-线下钱包充值订单，5.线上钱包充值）]不能为空")
    @ApiModelProperty("订单类型（0-视频订单，1-会员订单，3-线下钱包充值订单，5.线上钱包充值）")
    private Integer orderType;
    /**
     * 商家id（business.id）
     */
    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;
    /**
     * 商户订单号
     */
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("商户订单号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String mchntOrderNo;

    @ApiModelProperty("商户号")
    private String mchid;
    /**
     * 支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7:全币种)
     */
    @NotNull(message = "[支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7:全币种)]不能为空")
    @ApiModelProperty("支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7:全币种)")
    private Integer payType;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @ApiModelProperty(value = "支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）")
    private Integer payTypeDetail;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private Date payTime;
    /**
     * 需支付金额
     */
    @NotNull(message = "[需支付金额]不能为空")
    @ApiModelProperty("需支付金额")
    private BigDecimal payAmount;
    /**
     * 订单实付金额（单位：￥）
     */
    @NotNull(message = "[订单实付金额（单位：￥）]不能为空")
    @ApiModelProperty("订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;
    /**
     * 使用余额（单位：￥）
     */
    @NotNull(message = "[使用余额（单位：￥）]不能为空")
    @ApiModelProperty("使用余额（单位：￥）")
    private BigDecimal useBalance;

}

package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_模特搜索记录对象 order_video_model_search
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@ApiModel(value = "订单_视频_模特搜索记录对象 order_video_model_search")
@TableName("order_video_model_search")
@Data
public class OrderVideoModelSearch implements Serializable {

    private static final long serialVersionUID = 1064033792138382292L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 模特id
     */
    @NotNull(message = "[模特id]不能为空")
    @ApiModelProperty(value = "模特id", required = true)
    @Excel(name = "模特id")
    private Long modelId;

    /**
     * 搜索内容
     */
    @NotNull(message = "[搜索内容]不能为空")
    @ApiModelProperty(value = "搜索内容", required = true)
    @Excel(name = "搜索内容")
    private String content;

    /**
     * 是否删除（0:未删除,1:已删除）
     */
    @ApiModelProperty(value = "是否删除（0:未删除,1:已删除）", notes = "0:未删除,1:已删除", required = true)
    @NotNull(message = "[是否删除]不能为空")
    @Excel(name = "是否删除", readConverterExp = "0:未删除,1:已删除")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}

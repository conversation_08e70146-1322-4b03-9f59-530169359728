
package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateBatchDTO;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateDTO;
import com.ruoyi.system.api.domain.vo.biz.translate.TranslateVO;
import com.ruoyi.system.api.factory.RemoteTranslateFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 翻译服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteTranslateService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteTranslateFallbackFactory.class)
public interface RemoteTranslateService {

    /**
     * 批量翻译
     */
    @PostMapping("/translate")
    List<String> translateBatch(@RequestBody TranslateBatchDTO translateBatchDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 单句翻译
     */
    @PostMapping("/translate/translateStr")
    TranslateVO translateStr(@RequestBody @Validated TranslateDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_物流关联对象 order_video_logistic
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@ApiModel(value = "订单_视频_物流关联对象 order_video_logistic")
@TableName("order_video_logistic")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoLogistic implements Serializable {

    private static final long serialVersionUID = 5666213835635408589L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 视频id
     */
    @NotNull(message = "[视频id]不能为空")
    @ApiModelProperty(value = "视频id", required = true)
    @Excel(name = "视频id")
    private Long videoId;

    /**
     * 收件地址ID (FK:order_video_model_shipping_address.id)
     */
    @ApiModelProperty(value = "收件地址ID")
    private Long shippingAddressId;

    /**
     * 物流单号
     */
    @NotNull(message = "[物流单号]不能为空")
    @ApiModelProperty(value = "物流单号", required = true)
    @Excel(name = "物流单号")
    private String number;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;

    /**
     * 是否补发（0:补发,1:不是补发）
     */
    @ApiModelProperty(value = "是否补发（0:补发,1:不是补发）", notes = "0:补发,1:不是补发", required = true)
    @NotNull(message = "[是否补发]不能为空")
    @Excel(name = "是否补发", readConverterExp = "0:补发,1:不是补发")
    private Integer reissue;

    /**
     * 补发原因
     */
    @ApiModelProperty(value = "补发原因")
    private String reissueCause;

    /**
     * 发货时间（商家/运营 点击发货时间）
     */
    @ApiModelProperty(value = "发货时间（商家/运营 点击发货时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shippingTime;


    @ApiModelProperty(value = "是否确认收货：1:已收货,0:未收货")
    private Integer receipt;

    @ApiModelProperty(value = "确认收货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiptTime;

    /**
     * 标记物流状态（1:标记发货）
     */
    @ApiModelProperty(value = "标记物流状态（1:标记发货）")
    private Integer logisticFlag;

    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货原因")
    private String logisticFlagRemark;

    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date logisticFlagTime;


    @ApiModelProperty(value = "是否作废（0-否 1-是）")
    private Integer isCancel;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}

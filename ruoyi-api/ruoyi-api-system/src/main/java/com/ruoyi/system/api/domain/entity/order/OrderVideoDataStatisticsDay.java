package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-06-04 10:38:31
 */
@Data
@TableName("order_video_data_statistics_day")
public class OrderVideoDataStatisticsDay implements Serializable {
    private static final long serialVersionUID = 6008577580245027259L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 记录时间-开始
     */
    @ApiModelProperty(value = "记录时间-开始")
    private Date writeTimeBegin;

    /**
     * 记录时间-结束
     */
    @ApiModelProperty(value = "记录时间-结束")
    private Date writeTimeEnd;

    /**
     * 新增视频订单数量
     */
    @ApiModelProperty(value = "新增视频订单数量")
    private Long addedOrderVideoCount;

    /**
     * 新增视频任务单数量
     */
    @ApiModelProperty(value = "新增视频任务单数量")
    private Long addedOrderVideoTaskCount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

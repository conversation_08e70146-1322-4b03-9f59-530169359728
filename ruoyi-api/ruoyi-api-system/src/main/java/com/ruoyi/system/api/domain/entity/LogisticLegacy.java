package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 物流信息表
 * @TableName logistic_legacy
 */
@TableName(value ="logistic_legacy")
@Data
public class LogisticLegacy implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 物流单号
     */
    @TableField(value = "number")
    private String number;

    /**
     * 物流最新主状态
     */
    @TableField(value = "latest_main_status")
    private String latestMainStatus;

    /**
     * 物流最新子状态
     */
    @TableField(value = "latest_sub_status")
    private String latestSubStatus;

    /**
     * 物流跟踪状态（TRACKING_UPDATED:持续更新,TRACKING_STOPPED:停止跟踪）
     */
    @TableField(value = "event")
    private String event;

    /**
     * 运输商名称
     */
    @TableField(value = "carrier")
    private String carrier;

    /**
     * 运输商代码
     */
    @TableField(value = "carrier_code")
    private String carrierCode;

    /**
     * 运输商别名
     */
    @TableField(value = "carrier_alias")
    private String carrierAlias;

    /**
     * 运输商联系电话
     */
    @TableField(value = "carrier_tel")
    private String carrierTel;

    /**
     * 
     */
    @TableField(value = "carrier_homepage")
    private String carrierHomepage;

    /**
     * 运输商所属国家
     */
    @TableField(value = "carrier_country")
    private String carrierCountry;

    /**
     * 签收时间
     */
    @TableField(value = "sign_time")
    private Date signTime;

    /**
     * 发件地址_国家或地区（大写）
     */
    @TableField(value = "sa_country")
    private String saCountry;

    /**
     * 发件地址_州、省
     */
    @TableField(value = "sa_state")
    private String saState;

    /**
     * 发件地址_城市
     */
    @TableField(value = "sa_city")
    private String saCity;

    /**
     * 发件地址_街道
     */
    @TableField(value = "sa_street")
    private String saStreet;

    /**
     * 发件地址_邮编
     */
    @TableField(value = "sa_postal_code")
    private String saPostalCode;

    /**
     * 发件地址_经度
     */
    @TableField(value = "sa_longitude")
    private String saLongitude;

    /**
     * 发件地址_纬度
     */
    @TableField(value = "sa_latitude")
    private String saLatitude;

    /**
     * 收件地址_国家或地区（大写）
     */
    @TableField(value = "ra_country")
    private String raCountry;

    /**
     * 收件地址_州、省
     */
    @TableField(value = "ra_state")
    private String raState;

    /**
     * 收件地址_城市
     */
    @TableField(value = "ra_city")
    private String raCity;

    /**
     * 收件地址_街道
     */
    @TableField(value = "ra_street")
    private String raStreet;

    /**
     * 收件地址_邮编
     */
    @TableField(value = "ra_postal_code")
    private String raPostalCode;

    /**
     * 收件地址_经度
     */
    @TableField(value = "ra_longitude")
    private String raLongitude;

    /**
     * 收件地址_纬度
     */
    @TableField(value = "ra_latitude")
    private String raLatitude;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LogisticLegacy other = (LogisticLegacy) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getNumber() == null ? other.getNumber() == null : this.getNumber().equals(other.getNumber()))
            && (this.getLatestMainStatus() == null ? other.getLatestMainStatus() == null : this.getLatestMainStatus().equals(other.getLatestMainStatus()))
            && (this.getLatestSubStatus() == null ? other.getLatestSubStatus() == null : this.getLatestSubStatus().equals(other.getLatestSubStatus()))
            && (this.getEvent() == null ? other.getEvent() == null : this.getEvent().equals(other.getEvent()))
            && (this.getCarrier() == null ? other.getCarrier() == null : this.getCarrier().equals(other.getCarrier()))
            && (this.getCarrierCode() == null ? other.getCarrierCode() == null : this.getCarrierCode().equals(other.getCarrierCode()))
            && (this.getCarrierAlias() == null ? other.getCarrierAlias() == null : this.getCarrierAlias().equals(other.getCarrierAlias()))
            && (this.getCarrierTel() == null ? other.getCarrierTel() == null : this.getCarrierTel().equals(other.getCarrierTel()))
            && (this.getCarrierHomepage() == null ? other.getCarrierHomepage() == null : this.getCarrierHomepage().equals(other.getCarrierHomepage()))
            && (this.getCarrierCountry() == null ? other.getCarrierCountry() == null : this.getCarrierCountry().equals(other.getCarrierCountry()))
            && (this.getSignTime() == null ? other.getSignTime() == null : this.getSignTime().equals(other.getSignTime()))
            && (this.getSaCountry() == null ? other.getSaCountry() == null : this.getSaCountry().equals(other.getSaCountry()))
            && (this.getSaState() == null ? other.getSaState() == null : this.getSaState().equals(other.getSaState()))
            && (this.getSaCity() == null ? other.getSaCity() == null : this.getSaCity().equals(other.getSaCity()))
            && (this.getSaStreet() == null ? other.getSaStreet() == null : this.getSaStreet().equals(other.getSaStreet()))
            && (this.getSaPostalCode() == null ? other.getSaPostalCode() == null : this.getSaPostalCode().equals(other.getSaPostalCode()))
            && (this.getSaLongitude() == null ? other.getSaLongitude() == null : this.getSaLongitude().equals(other.getSaLongitude()))
            && (this.getSaLatitude() == null ? other.getSaLatitude() == null : this.getSaLatitude().equals(other.getSaLatitude()))
            && (this.getRaCountry() == null ? other.getRaCountry() == null : this.getRaCountry().equals(other.getRaCountry()))
            && (this.getRaState() == null ? other.getRaState() == null : this.getRaState().equals(other.getRaState()))
            && (this.getRaCity() == null ? other.getRaCity() == null : this.getRaCity().equals(other.getRaCity()))
            && (this.getRaStreet() == null ? other.getRaStreet() == null : this.getRaStreet().equals(other.getRaStreet()))
            && (this.getRaPostalCode() == null ? other.getRaPostalCode() == null : this.getRaPostalCode().equals(other.getRaPostalCode()))
            && (this.getRaLongitude() == null ? other.getRaLongitude() == null : this.getRaLongitude().equals(other.getRaLongitude()))
            && (this.getRaLatitude() == null ? other.getRaLatitude() == null : this.getRaLatitude().equals(other.getRaLatitude()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNumber() == null) ? 0 : getNumber().hashCode());
        result = prime * result + ((getLatestMainStatus() == null) ? 0 : getLatestMainStatus().hashCode());
        result = prime * result + ((getLatestSubStatus() == null) ? 0 : getLatestSubStatus().hashCode());
        result = prime * result + ((getEvent() == null) ? 0 : getEvent().hashCode());
        result = prime * result + ((getCarrier() == null) ? 0 : getCarrier().hashCode());
        result = prime * result + ((getCarrierCode() == null) ? 0 : getCarrierCode().hashCode());
        result = prime * result + ((getCarrierAlias() == null) ? 0 : getCarrierAlias().hashCode());
        result = prime * result + ((getCarrierTel() == null) ? 0 : getCarrierTel().hashCode());
        result = prime * result + ((getCarrierHomepage() == null) ? 0 : getCarrierHomepage().hashCode());
        result = prime * result + ((getCarrierCountry() == null) ? 0 : getCarrierCountry().hashCode());
        result = prime * result + ((getSignTime() == null) ? 0 : getSignTime().hashCode());
        result = prime * result + ((getSaCountry() == null) ? 0 : getSaCountry().hashCode());
        result = prime * result + ((getSaState() == null) ? 0 : getSaState().hashCode());
        result = prime * result + ((getSaCity() == null) ? 0 : getSaCity().hashCode());
        result = prime * result + ((getSaStreet() == null) ? 0 : getSaStreet().hashCode());
        result = prime * result + ((getSaPostalCode() == null) ? 0 : getSaPostalCode().hashCode());
        result = prime * result + ((getSaLongitude() == null) ? 0 : getSaLongitude().hashCode());
        result = prime * result + ((getSaLatitude() == null) ? 0 : getSaLatitude().hashCode());
        result = prime * result + ((getRaCountry() == null) ? 0 : getRaCountry().hashCode());
        result = prime * result + ((getRaState() == null) ? 0 : getRaState().hashCode());
        result = prime * result + ((getRaCity() == null) ? 0 : getRaCity().hashCode());
        result = prime * result + ((getRaStreet() == null) ? 0 : getRaStreet().hashCode());
        result = prime * result + ((getRaPostalCode() == null) ? 0 : getRaPostalCode().hashCode());
        result = prime * result + ((getRaLongitude() == null) ? 0 : getRaLongitude().hashCode());
        result = prime * result + ((getRaLatitude() == null) ? 0 : getRaLatitude().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", number=").append(number);
        sb.append(", latestMainStatus=").append(latestMainStatus);
        sb.append(", latestSubStatus=").append(latestSubStatus);
        sb.append(", event=").append(event);
        sb.append(", carrier=").append(carrier);
        sb.append(", carrierCode=").append(carrierCode);
        sb.append(", carrierAlias=").append(carrierAlias);
        sb.append(", carrierTel=").append(carrierTel);
        sb.append(", carrierHomepage=").append(carrierHomepage);
        sb.append(", carrierCountry=").append(carrierCountry);
        sb.append(", signTime=").append(signTime);
        sb.append(", saCountry=").append(saCountry);
        sb.append(", saState=").append(saState);
        sb.append(", saCity=").append(saCity);
        sb.append(", saStreet=").append(saStreet);
        sb.append(", saPostalCode=").append(saPostalCode);
        sb.append(", saLongitude=").append(saLongitude);
        sb.append(", saLatitude=").append(saLatitude);
        sb.append(", raCountry=").append(raCountry);
        sb.append(", raState=").append(raState);
        sb.append(", raCity=").append(raCity);
        sb.append(", raStreet=").append(raStreet);
        sb.append(", raPostalCode=").append(raPostalCode);
        sb.append(", raLongitude=").append(raLongitude);
        sb.append(", raLatitude=").append(raLatitude);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteLogisticService;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 物流服务降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteLogisticFallbackFactory implements FallbackFactory<RemoteLogisticService> {

    @Override
    public RemoteLogisticService create(Throwable throwable) {
        log.error("物流服务调用失败");
        return new RemoteLogisticService() {
            @Override
            public List<LogisticVO> selectListByNumbers(Collection<String> numbers, String source) {
                log.error("查询物流信息失败:{}", throwable.getMessage());
                return List.of();
            }

            @Override
            public Boolean register(Collection<String> numbers, String source) {
                log.error("注册物流单号失败:{}", throwable.getMessage());
                return false;
            }

            @Override
            public Collection<String> getNumbersByCondition(LogisticListDTO dto, String source) {
                log.error("通过条件查询物流单号失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<LogisticInfoVO> getLastLogisticInfo(Collection<String> numbers, String source) {
                log.error("通过物流单号获取最新的物流信息失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }
        };
    }
}

package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.factory.RemoteDictDataFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 数据字典信息服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "RemoteDictDataService", value = ServiceNameConstants.SYSTEM_SERVICE,
        fallbackFactory = RemoteDictDataFallbackFactory.class)
public interface RemoteDictDataService {

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/dict/data/type/{dictType}")
    List<SysDictData> selectDictDataByType(@PathVariable(value = "dictType") String dictType, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

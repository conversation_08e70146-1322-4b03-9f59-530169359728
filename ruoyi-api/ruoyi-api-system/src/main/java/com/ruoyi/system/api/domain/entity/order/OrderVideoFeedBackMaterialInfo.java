package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单反馈(模特)_详细信息对象 order_video_feed_back_material_info
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@ApiModel(value = "订单反馈(模特)_详细信息对象 order_video_feed_back_material_info")
@TableName("order_video_feed_back_material_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderVideoFeedBackMaterialInfo implements Serializable {
    private static final long serialVersionUID = -7596474876727281415L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 素材id （FK:order_video_feed_back_material.id）
     */
    @ApiModelProperty(value = "素材id （FK:order_video_feed_back_material.id）")
    private Long materialId;

    /**
     * 任务详情ID
     */
    @ApiModelProperty(value = "任务详情ID")
    private Long taskDetailId;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;

    /**
     * 上传对象（2:运营,3:模特）
     */
    @ApiModelProperty(value = "上传对象（2:运营,3:模特）")
    private Integer object;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    private Long userId;

    /**
     * 领取编码
     */
    @ApiModelProperty(value = "领取编码")
    private Integer getCode;

    /**
     * 领取人姓名
     */
    @ApiModelProperty(value = "领取人姓名")
    private String getBy;

    /**
     * 领取人ID
     */
    @ApiModelProperty(value = "领取人ID")
    private Long getById;

    /**
     * 领取时间
     */
    @ApiModelProperty(value = "领取时间")
    private Date getTime;

    /**
     * 领取状态（0：待领取，1：已领取）
     */
    @ApiModelProperty(value = "领取状态（0：待领取，1：已领取）")
    private Integer getStatus;

    /**
     * 标记下载（0：未标记，1：已标记）
     */
    @ApiModelProperty(value = "标记下载（0：未标记，1：已标记）")
    private Integer downloadFlag;

    /**
     * 标记下载时间
     */
    @ApiModelProperty(value = "标记下载时间")
    private Date downloadFlagTime;

    /**
     * 剪辑人姓名
     */
    @ApiModelProperty(value = "剪辑人姓名")
    private String editBy;

    /**
     * 剪辑人ID
     */
    @ApiModelProperty(value = "剪辑人ID")
    private Long editById;

    /**
     * 剪辑时间
     */
    @ApiModelProperty(value = "剪辑时间")
    private Date editTime;

    /**
     * 反馈人姓名
     */
    @ApiModelProperty(value = "反馈人姓名")
    private String feedbackBy;

    /**
     * 反馈人ID
     */
    @ApiModelProperty(value = "反馈人ID")
    private Long feedbackById;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    private Date feedbackTime;

    /**
     * 反馈状态（1：已反馈，2：不反馈给商家，4：联动关闭）
     */
    @ApiModelProperty(value = "反馈状态（1：已反馈，2：不反馈给商家，4：联动关闭）")
    private Integer feedbackStatus;

    /**
     * 反馈状态备注
     */
    @ApiModelProperty(value = "反馈状态备注")
    private String feedbackRemark;

    /**
     * 状态（1：待下载，2：待剪辑，3：待反馈，4：需确认，5：已关闭）
     */
    @ApiModelProperty(value = "状态（1：待下载，2：待剪辑，3：待反馈，4：需确认，5：已关闭）")
    private Integer status;

    /**
     * 状态时间
     */
    @ApiModelProperty(value = "状态时间")
    private Date statusTime;

    /**
     * 进入待下载时间
     */
    @ApiModelProperty(value = "进入待下载时间")
    private Date enterTheDownloadTime;

    /**
     * 关闭人姓名
     */
    @ApiModelProperty(value = "关闭人姓名")
    private String closeBy;

    /**
     * 关闭人ID
     */
    @ApiModelProperty(value = "关闭人ID")
    private Long closeById;

    /**
     * 关闭原因（2：不反馈给商家，3：订单回退，4：联动关闭）
     */
    @ApiModelProperty(value = "关闭原因（2：不反馈给商家，3：订单回退，4：联动关闭）")
    private Integer closeReason;

    /**
     * 关闭人类型（0：运营，1：商家）
     */
    @ApiModelProperty(value = "关闭人类型（0：运营，1：商家）")
    private Integer closeByType;

    /**
     * 视频评分
     */
    @ApiModelProperty(value = "视频评分")
    private Float videoScore;

    /**
     * 视频评分内容
     */
    @ApiModelProperty(value = "视频评分内容")
    private String videoScoreContent;

    /**
     * 视频评分人
     */
    @ApiModelProperty("视频评分人")
    private String videoScoreBy;

    /**
     * 视频评分人ID
     */
    @ApiModelProperty("视频评分人ID")
    private Long videoScoreById;

    /**
     * 视频评分时间
     */
    @ApiModelProperty("视频评分时间")
    private Date videoScoreTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

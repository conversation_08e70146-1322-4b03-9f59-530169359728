package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:09
 */
@Data
@TableName("order_video_reminder_record")
public class OrderVideoReminderRecord implements Serializable {
    private static final long serialVersionUID = -4211958860276588427L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    @Excel(name = "视频id")
    private Long videoId;

    /**
     * 催单次数（累计）
     */
    @ApiModelProperty(value = "催单次数（累计）")
    @Excel(name = "催单次数（累计）")
    private Integer reminder;

    /**
     * 催单时间
     */
    @ApiModelProperty(value = "催单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reminderTime;

    /**
     * 催单状态（1:未处理,2:已确认,3:已完成）
     */
    @ApiModelProperty(value = "催单状态（1:未处理,2:已确认,3:已完成）")
    @Excel(name = "催单状态（1:未处理,2:已确认,3:已完成）")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:34
 */
@Data
@TableName("order_video_back_modify_amount_records")
public class OrderVideoBackModifyAmountRecords implements Serializable {

    private static final long serialVersionUID = 7021915103002206838L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 原视频价格（单位：$）
     */
    @ApiModelProperty(value = "原视频价格（单位：$）")
    private BigDecimal oldVideoPrice;

    /**
     * 新视频价格（单位：$）
     */
    @ApiModelProperty(value = "新视频价格（单位：$）")
    private BigDecimal newVideoPrice;

    /**
     * 原图片费用（单位：$）
     */
    @ApiModelProperty(value = "原图片费用（单位：$）")
    private BigDecimal oldPicPrice;

    /**
     * 新图片费用（单位：$）
     */
    @ApiModelProperty(value = "新图片费用（单位：$）")
    private BigDecimal newPicPrice;

    /**
     * 原佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "原佣金代缴税费（单位：$）")
    private BigDecimal oldCommissionPaysTaxes;

    /**
     * 新佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "新佣金代缴税费（单位：$）")
    private BigDecimal newCommissionPaysTaxes;

    /**
     * 原手续费（单位：$）
     */
    @ApiModelProperty(value = "原手续费（单位：$）")
    private BigDecimal oldExchangePrice;

    /**
     * 新手续费（单位：$）
     */
    @ApiModelProperty(value = "新手续费（单位：$）")
    private BigDecimal newExchangePrice;

    /**
     * 原服务费（单位：$）
     */
    @ApiModelProperty(value = "原服务费（单位：$）")
    private BigDecimal oldServicePrice;

    /**
     * 新服务费（单位：$）
     */
    @ApiModelProperty(value = "新服务费（单位：$）")
    private BigDecimal newServicePrice;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createBy;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
}

package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单收款账号表
 *
 * <AUTHOR>
 * @TableName order_payee_account
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayeeAccount implements Serializable {

    private static final long serialVersionUID = 6322334196904203448L;

    @NotNull(message = "[主键，自增ID]不能为空")
    @ApiModelProperty("主键，自增ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "[订单编号 (FK:order_table.order_num)]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("订单编号 (FK:order_table.order_num)")
    @Length(max = 30, message = "编码长度不能超过30")
    private String orderNum;

    @NotNull(message = "[账号类型（0-默认类型, 1-银行卡账号, 2-对公账号，3-全币种账号）]不能为空")
    @ApiModelProperty("账号类型（0-默认类型, 1-银行卡账号, 2-对公账号，3-全币种账号）")
    private Integer accountType;

    @NotBlank(message = "[公司名称/账号名称]不能为空")
    @ApiModelProperty("公司名称/账号名称")
    private String accountName;

    @NotBlank(message = "[收款账号名称/银行账号]不能为空")
    @ApiModelProperty("收款账号名称/银行账号")
    private String bankAccount;

    @NotBlank(message = "[开户行名称/银行所在地]不能为空")
    @ApiModelProperty("开户行名称/银行所在地")
    private String bankName;

    @ApiModelProperty("收款账号类型")
    private String companyAccountType;

    @ApiModelProperty("银行代码")
    private String companyBankCode;

    @ApiModelProperty("分行代码")
    private String companyBankSubCode;

    @ApiModelProperty("SWIFT代码")
    private String companyBankSwiftCode;

    @ApiModelProperty("收款人地址")
    private String companyBankPayeeAddress;

    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

}

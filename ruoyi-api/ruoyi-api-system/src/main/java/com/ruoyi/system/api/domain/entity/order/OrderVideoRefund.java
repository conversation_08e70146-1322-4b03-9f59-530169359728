package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单_视频_退款对象 order_video_refund
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@ApiModel(value = "订单_视频_退款对象 order_video_refund")
@TableName("order_video_refund")
@Data
public class OrderVideoRefund implements Serializable {

    private static final long serialVersionUID = -8112147335003914065L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 视频订单id
     */
    @NotNull(message = "[视频订单id]不能为空")
    @ApiModelProperty(value = "视频订单id", required = true)
    @Excel(name = "视频订单id")
    private Long videoId;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 订单号
     */
    @NotNull(message = "[订单号]不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    @Excel(name = "订单号")
    private String orderNum;

    /**
     * 退款审批号
     */
    @NotNull(message = "[退款审批号]不能为空")
    @ApiModelProperty(value = "退款审批号", required = true)
    @Excel(name = "退款审批号")
    private String refundNum;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    @Excel(name = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    @Excel(name = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    @Excel(name = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    @Excel(name = "产品链接")
    private String productLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    @Excel(name = "使用平台", readConverterExp = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    /** 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国） */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    @Excel(name = "拍摄国家", readConverterExp = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    @Excel(name = "照片数量", readConverterExp = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;


    /**
     * 申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成）
     */
    @ApiModelProperty(value = "申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成）")
    @Excel(name = "申请时订单状态", readConverterExp = "1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成")
    private Integer status;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @Excel(name = "拍摄模特id")
    private Long shootModelId;

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    @Excel(name = "对接人id")
    private Long contactId;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @Excel(name = "出单人id")
    private Long issueId;

    /**
     * 视频金额（单位：￥）
     */
    @ApiModelProperty(value = "视频金额（单位：￥）", notes = "单位：￥")
    @Excel(name = "视频金额", readConverterExp = "单位：￥")
    private BigDecimal amount;

    /**
     * 视频金额 - 视频差额
     */
    @ApiModelProperty(value = "实际视频金额（单位：￥）", notes = "单位：￥")
    private BigDecimal realAmount;

    /**
     * 退款金额（单位：￥）
     */
    @ApiModelProperty(value = "退款金额（单位：￥）", notes = "单位：￥")
    @Excel(name = "退款金额", readConverterExp = "单位：￥")
    private BigDecimal refundAmount;

    /**
     * 视频已退金额
     */
    @ApiModelProperty(value = "视频已退金额", notes = "单位：￥")
    @Excel(name = "视频已退金额", readConverterExp = "单位：￥")
    private BigDecimal refundAmountTotal;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）", notes = "1:补偿,2:取消订单,3:取消选配")
    @Excel(name = "退款类型", readConverterExp = "1:补偿,2:取消订单,3:取消选配")
    private Integer refundType;

    /**
     * 发起方（1:商家,2:平台）
     */
    @ApiModelProperty(value = "发起方（1:商家,2:平台）", notes = "1:商家,2:平台")
    @Excel(name = "发起方", readConverterExp = "1:商家,2:平台")
    private Integer initiatorSource;

    /**
     * 发起人名称
     */
    @ApiModelProperty(value = "发起人名称")
    @Excel(name = "发起人名称")
    private String initiatorName;

    /**
     * 退款原因
     */
    @ApiModelProperty(value = "退款原因")
    @Excel(name = "退款原因")
    private String refundCause;

    /**
     * 退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）
     */
    @ApiModelProperty(value = "退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）", notes = "0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功")
    @Excel(name = "退款状态", readConverterExp = "0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功")
    private Integer refundStatus;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operateBy;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operateById;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    /**
     * 拒绝理由
     */
    @ApiModelProperty(value = "拒绝理由")
    @Excel(name = "拒绝理由")
    private String rejectCause;


    @ApiModelProperty(value = "同意审核备注")
    @Excel(name = "备注")
    private String remark;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    @Excel(name = "商家id")
    private Long businessId;

    @ApiModelProperty(value = "是否是取消订单")
    private Integer isCancelOrder;

    /**
     * 是否全额退款（0:是,1:不是）
     */
    @ApiModelProperty(value = "是否全额退款（0:是,1:不是）")
    @Excel(name = "是否全额退款（0:是,1:不是）")
    private Integer isFullRefund;

    @ApiModelProperty(value = "是否全额退款照片（0:是,1:不是）")
    private Integer isFullRefundPic;

    /**
     * 售后单/工单 ID （多个逗号隔开）
     */
    @ApiModelProperty(value = "售后单/工单 ID （多个逗号隔开）")
    private String taskDetailId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

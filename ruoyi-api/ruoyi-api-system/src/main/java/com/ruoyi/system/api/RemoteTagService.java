
package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.vo.TagListVO;
import com.ruoyi.system.api.factory.RemoteTagFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Collection;
import java.util.List;

/**
 * 标签服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteTagService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteTagFallbackFactory.class)
public interface RemoteTagService {


    /**
     * 根据标签id获取标签信息
     */
    @PostMapping(value = "/tag/query-list")
    public List<TagListVO> queryList(@RequestBody Collection<Long> tagIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

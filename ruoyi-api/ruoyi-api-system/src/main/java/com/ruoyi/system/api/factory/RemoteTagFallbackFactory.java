package com.ruoyi.system.api.factory;

import cn.hutool.core.collection.ListUtil;
import com.ruoyi.system.api.RemoteTagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 模特服务降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteTagFallbackFactory implements FallbackFactory<RemoteTagService> {

    @Override
    public RemoteTagService create(Throwable throwable) {
        log.error("标签服务调用失败");
        return (tagIds, source) -> {
            log.error("根据标签id获取标签信息失败:{}", throwable.getMessage());
            return ListUtil.empty();
        };
    }
}

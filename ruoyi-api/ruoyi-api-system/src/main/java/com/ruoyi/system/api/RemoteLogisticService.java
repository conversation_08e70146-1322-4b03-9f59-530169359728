package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import com.ruoyi.system.api.factory.RemoteLogisticFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Collection;
import java.util.List;

/**
 * 物流服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteLogisticService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteLogisticFallbackFactory.class)
public interface RemoteLogisticService {

    /**
     * 通过物流单号查询物流信息
     *
     * @return 物流信息
     */
    @PostMapping("/logistic/list")
    public List<LogisticVO> selectListByNumbers(@RequestBody Collection<String> numbers
            , @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册物流单号
     */
    @PostMapping("/logistic/register")
    public Boolean register(@RequestBody Collection<String> numbers
            , @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过条件查询物流单号
     */
    @PostMapping("/logistic/get-numbers")
    public Collection<String> getNumbersByCondition(@RequestBody LogisticListDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过物流单号获取最新的物流信息
     */
    @PostMapping("/logistic/get-last-logistic-info")
    public List<LogisticInfoVO> getLastLogisticInfo(@RequestBody Collection<String> numbers, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

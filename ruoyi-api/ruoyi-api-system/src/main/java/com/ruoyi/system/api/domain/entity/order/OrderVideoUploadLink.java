package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/26 17:50
 */
@Data
@TableName("order_video_upload_link")
public class OrderVideoUploadLink implements Serializable {
    private static final long serialVersionUID = -1966558174675901212L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频id (FK:order_video.id)")
    private Long videoId;

    /**
     * 提交信息对象（1:商家,2:运营）
     */
    @ApiModelProperty(value = "提交信息对象（1:商家,2:运营）")
    private Integer object;

    /**
     * 提交信息用户id
     */
    @ApiModelProperty(value = "提交信息用户id")
    private Long userId;

    /**
     * 提交信息时间
     */
    @ApiModelProperty(value = "提交信息时间")
    private Date time;

    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    private String needUploadLink;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）")
    private Integer status;

    /**
     * 状态时间
     */
    @ApiModelProperty(value = "状态时间")
    private Date statusTime;

    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    /**
     * 视频标题首次信息
     */
    @ApiModelProperty(value = "视频标题首次信息")
    private String videoTitleFirst;

    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图URI")
    private String videoCover;

    /**
     * 视频封面图URI首次信息
     */
    @ApiModelProperty(value = "视频封面图URI首次信息")
    private String videoCoverFirst;

    /**
     * 上传的链接
     */
    @ApiModelProperty(value = "上传的链接")
    private String uploadLink;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    private Long uploadUserId;

    /**
     * 上传用户名称
     */
    @ApiModelProperty(value = "上传用户名称")
    private String uploadUserName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;

    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String remark;

    /**
     * 关闭人姓名
     */
    @ApiModelProperty(value = "关闭人姓名")
    private String closeBy;

    /**
     * 关闭人ID
     */
    @ApiModelProperty(value = "关闭人ID")
    private Long closeById;

    /**
     * 关闭原因（1：取消上传）
     */
    @ApiModelProperty(value = "关闭原因（1：取消上传）")
    private Integer closeReason;

    /**
     * asin
     */
    @ApiModelProperty(value = "asin")
    private String asin;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号")
    private String uploadAccount;

    /**
     * 操作备注
     */
    @ApiModelProperty(value = "操作备注")
    private String operateRemark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

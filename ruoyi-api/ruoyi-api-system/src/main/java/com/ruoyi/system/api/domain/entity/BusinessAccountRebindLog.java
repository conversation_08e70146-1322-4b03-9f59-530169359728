package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.Date;

/**
 * 重新绑定日志表
 */
@Data
public class BusinessAccountRebindLog {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;
    @ApiModelProperty(value = "商家id")
    private Long businessId;
    @ApiModelProperty(value = "用户id")
    private Long bizUserId;

    @ApiModelProperty(value = "申请表id")
    private Long applyId;

    private String name;
    private String nickName;
    private String phone;
    private String connectUserName;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "重新绑定状态 0,新增（正常），解绑（1）已解绑")
    private Integer status;

    @ApiModelProperty(value = "主账号")
    private String account;

    private Date auditTime;
    private Integer auditStatus;
}

package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 模特关联订单
 *
 * <AUTHOR>
 * @date 2024/6/14 14:16
 */
@ApiModel(value = "模特关联订单 order_video_model")
@TableName("order_video_model")
@Data
public class OrderVideoModel implements Serializable {

    private static final long serialVersionUID = -1352473001706477914L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;
    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private Long modelId;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 接单时间
     */
    @ApiModelProperty(value = "接单时间")
    private Date acceptTime;

    /**
     * 超时时间
     */
    @ApiModelProperty(value = "超时时间")
    private Date overTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}

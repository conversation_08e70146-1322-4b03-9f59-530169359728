package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:05
 */
@TableName("order_video_change_log")
@Data
public class OrderVideoChangeLog implements Serializable {
    private static final long serialVersionUID = -1076318089904193200L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty("视频id")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 变更时间
     */
    @ApiModelProperty("变更时间")
    private Date changeTime;

    /**
     * 变更操作运营
     */
    @ApiModelProperty("变更操作运营")
    private Long changeUserId;

    /**
     * 变更类型（预留字段）
     */
    @ApiModelProperty("变更类型（预留字段）")
    private Integer changeType;

    /**
     * 记录类型（1:初始记录,2:编辑订单时修改,3:商家同意后修改）
     */
    @ApiModelProperty("记录类型（1:初始记录,2:编辑订单时修改,3:商家同意后修改）")
    private Integer logType;
}

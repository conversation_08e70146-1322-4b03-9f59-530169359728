
package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.factory.RemoteAmazonFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * Amazon服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteAmazonService", value = ServiceNameConstants.BIZ_SERVICE, fallbackFactory = RemoteAmazonFallbackFactory.class)
public interface RemoteAmazonService {
    /**
     * 异步批量抓取amazon产品链接图片并更新视频订单
     */
    @PostMapping("/amazon/async-update-order-video-image")
    public void asyncUpdateOrderVideoImage(@RequestBody AsyncCrawlTask asyncCrawlTask, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}

package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.entity.SysDictData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;


@FeignClient(contextId = "remoteSysDictTypeService", value = ServiceNameConstants.SYSTEM_SERVICE)
public interface RemoteSysDictTypeService {

    @GetMapping(value = "/dict/data/getTypeList/{dictType}")
    public List<SysDictData> queryDictType(@PathVariable("dictType") String dictType, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

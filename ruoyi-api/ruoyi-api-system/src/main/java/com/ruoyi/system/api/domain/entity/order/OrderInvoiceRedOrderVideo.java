package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:44
 */
@Data
@TableName("order_invoice_red_order_video")
public class OrderInvoiceRedOrderVideo implements Serializable {
    private static final long serialVersionUID = -5050228845896059048L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 红冲订单ID (FK:order_invoice_red_order.id)
     */
    @ApiModelProperty(value = "红冲订单ID")
    private Long invoiceRedOrderId;

    @ApiModelProperty(value = "红冲订单ID")
    private String orderNum;

    /**
     * 视频订单ID (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）")
    private Integer refundType;

    /**
     * 提现时间
     */
    @ApiModelProperty(value = "提现时间")
    private Date withdrawDepositTime;

    /**
     * 提现金额
     */
    @ApiModelProperty(value = "提现金额")
    private BigDecimal withdrawDepositAmount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

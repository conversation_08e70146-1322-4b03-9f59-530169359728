package com.ruoyi.system.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 模特对接人员对象 model_person
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@ApiModel(value = "模特对接人员对象 model_person")
@TableName("model_person")
@Data
public class ModelPerson implements Serializable
{

    private static final long serialVersionUID = 8509563640246826088L;
    /** 主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /** 模特id */
    @ApiModelProperty(value = "模特id")
    @Excel(name = "模特id")
    private Long modelId;

    /** 对接人id */
    @ApiModelProperty(value = "对接人id")
    @Excel(name = "对接人id")
    private Long userId;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

}

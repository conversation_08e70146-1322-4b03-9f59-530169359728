package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员渠道记录表
 *
 * <AUTHOR>
 * @TableName order_member_channel
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderMemberChannel implements Serializable {


    private static final long serialVersionUID = 906076343718230318L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @NotNull(message = "[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 渠道id
     */
    @NotNull(message = "[渠道id（distribution_channel.id）]不能为空")
    @ApiModelProperty("渠道id（distribution_channel.id）")
    private Long channelId;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("登录用户ID FK distribution_channel.biz_user_id")
    private Long channelBizUserId;

    /**
     * 渠道名称
     */
    @NotBlank(message = "[渠道名称（distribution_channel.channel_name）]不能为空")
    @Size(max = 27, message = "编码长度不能超过27")
    @ApiModelProperty("渠道名称（distribution_channel.channel_name）")
    @Length(max = 27, message = "编码长度不能超过27")
    private String channelName;

    /**
     * 渠道手机号
     */
    @NotBlank(message = "[渠道手机号（biz_user.phone）]不能为空")
    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("渠道手机号（biz_user.phone）")
    @Length(max = 15, message = "编码长度不能超过15")
    private String channelPhone;

    @ApiModelProperty("渠道种草码")
    private String seedCode;

    /**
     * 商家id
     */
    @NotNull(message = "[商家id（business.id）]不能为空")
    @ApiModelProperty("商家id（business.id）")
    private Long businessId;

    /**
     * 商家名称
     */
    @NotBlank(message = "[商家名称（business.name）]不能为空")
    @Size(max = 50, message = "编码长度不能超过20")
    @ApiModelProperty("商家名称（business.name）")
    @Length(max = 50, message = "编码长度不能超过20")
    private String businessName;

    /**
     * 商家端登录用户id FK:biz_user.id
     */
    @ApiModelProperty("商家端登录用户id FK:biz_user.id")
    private Long bizUserId;

    /**
     * 商家端登录用户微信昵称
     */
    @ApiModelProperty("商家端登录用户微信昵称")
    private String bizUserNickName;

    /**
     * 商家端登录用户手机号
     */
    @ApiModelProperty("商家端登录用户手机号")
    private String bizUserPhone;

    /**
     * 会员编码
     */
    @NotBlank(message = "[会员编码（business.member_code）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("会员编码（business.member_code）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String memberCode;

    /**
     * 套餐类型：0=季度会员,1=年度会员,2=三年会员
     */
    @NotNull(message = "[套餐类型：0=季度会员,1=年度会员,2=三年会员]不能为空")
    @ApiModelProperty("套餐类型：0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 订单实付金额
     */
    @ApiModelProperty(value = "订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    /**
     * 订单实付金额（对应币种实付）
     */
    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    /**
     * 币种（详见sys_dict_type.dict_type = sys_money_type）
     */
    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    /**
     * 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
     */
    @ApiModelProperty("支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）")
    private Integer payType;

    /**
     * 结算比例
     */
    @NotNull(message = "[结算比例]不能为空")
    @ApiModelProperty("结算比例")
    private BigDecimal settleRage;

    /**
     * 最终结算金额
     */
    @NotNull(message = "[最终结算金额（结算比例 * 会员价格）]不能为空")
    @ApiModelProperty("最终结算金额（结算比例 * 会员价格）")
    private BigDecimal settleAmount;

    @ApiModelProperty("实际结算金额")
    private BigDecimal realSettleAmount;

    /**
     * 结算时间
     */
    @ApiModelProperty("结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /**
     * 状态（0-未结算，1-已结算）
     */
    @NotNull(message = "[状态（0-未结算，1-已结算）]不能为空")
    @ApiModelProperty("状态（0-未结算，1-已结算）")
    private Integer settleStatus;

    /**
     * 结算凭证地址
     */
    @NotBlank(message = "[结算凭证地址]不能为空")
    @Size(max = 64, message = "编码长度不能超过64")
    @ApiModelProperty("结算凭证地址")
    @Length(max = 64, message = "编码长度不能超过64")
    private String settleResourceUrl;

    /**
     * 结算人名字
     */
    @NotBlank(message = "[结算人名字（sys_user.user_name）]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("结算人名字（sys_user.user_name）")
    @Length(max = 30, message = "编码长度不能超过30")
    private String settleUserName;

    /**
     * 结算人id
     */
    @NotNull(message = "[结算人id（sys_user.user_id）]不能为空")
    @ApiModelProperty("结算人id（sys_user.user_id）")
    private Long settleUserId;

    /**
     * 备注
     */
    @NotBlank(message = "[备注]不能为空")
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("备注")
    @Length(max = 100, message = "编码长度不能超过100")
    private String remark;

    /**
     * 购买时间
     */
    @ApiModelProperty("购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间（当前需求只有一次操作，即运营点击结算的时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

}

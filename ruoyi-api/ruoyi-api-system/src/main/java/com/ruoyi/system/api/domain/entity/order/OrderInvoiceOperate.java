package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:12
 */
@Data
@TableName("order_invoice_operate")
public class OrderInvoiceOperate implements Serializable {
    private static final long serialVersionUID = 8907754050724795922L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票ID (FK:order_invoice.id)
     */
    @ApiModelProperty(value = "发票ID")
    private Long invoiceId;

    /**
     * 操作类型（详见OrderInvoiceOperateTypeEnum）
     */
    @ApiModelProperty(value = "操作类型（1=申请开票, 2=取消开票, 3=确认开票, 4=修改开票信息, 5=上传发票, 6=重新上传发票, 7=审核发票, 8=红冲提醒, 9=标记红冲, 10=标记红冲, 11=标记红冲, 12=重开发票, 13=红冲重开）")
    private Integer type;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 图片URI
     */
    @ApiModelProperty(value = "图片URI")
    private String objectKey;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 图片URI
     */
    @ApiModelProperty(value = "发票文件URI")
    @TableField(exist = false)
    private List<String> objectKeys;

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
        if (StrUtil.isNotBlank(objectKey)) {
            this.objectKeys = StrUtil.split(objectKey, StrUtil.COMMA);
        }
    }
}

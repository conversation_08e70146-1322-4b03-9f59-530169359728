package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:44
 */
@Data
@TableName("order_invoice_order_video")
public class OrderInvoiceOrderVideo implements Serializable {
    private static final long serialVersionUID = -2447675849548840226L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票订单ID（FK：order_invoice_order.id）
     */
    @ApiModelProperty(value = "发票订单ID")
    private Long invoiceOrderId;

    /**
     * 视频订单ID (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

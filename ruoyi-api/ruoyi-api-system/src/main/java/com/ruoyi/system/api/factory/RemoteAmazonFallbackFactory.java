package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteAmazonService;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.entity.biz.amazon.AmazonGoodsPic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * Amazon服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteAmazonFallbackFactory implements FallbackFactory<RemoteAmazonService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteAmazonFallbackFactory.class);

    @Override
    public RemoteAmazonService create(Throwable throwable) {
        log.error("Amazon服务调用失败");
        return (asyncCrawlTask, source) -> log.error("异步批量抓取amazon产品链接图片并更新视频订单失败:{}", throwable.getMessage());
    }
}

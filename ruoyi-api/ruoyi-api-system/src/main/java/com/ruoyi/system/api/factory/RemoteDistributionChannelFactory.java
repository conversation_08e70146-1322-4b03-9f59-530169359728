package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteDistributionChannelService;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderBatchDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:37
 */
@Component
@Slf4j
public class RemoteDistributionChannelFactory implements FallbackFactory<RemoteDistributionChannelService> {

    @Override
    public RemoteDistributionChannelService create(Throwable throwable) {
        log.error("分销渠道服务调用失败");

        return new RemoteDistributionChannelService() {
//            @Override
//            public BigDecimal getDistributionChannelBySeedCode(String seedCode, String source) {
//                log.error("调用 [根据种草码 获取分销渠道信息] 失败:{}", throwable.getLocalizedMessage());
//                return null;
//            }

            @Override
            public ChannelBrokeRageVO getFissionBrokeRageBySeedCode(String seedCode, String source) {
                log.error("调用 [根据种草码 获取渠道信息] 失败:{}", throwable.getLocalizedMessage());
                return null;
            }

            @Override
            public DistributionChannel getDistributionChannelEntityBySeedCode(String seedCode, String source) {
                log.error("调用 [根据种草码 获取分销渠道信息] 失败:{}", throwable.getLocalizedMessage());
                return null;
            }

            @Override
            public List<DistributionChannel> queryDistributionChannelsBySeedCodes(Collection<String> numbers, String source) {
                log.error("调用 [根据种草码列表 获取分销渠道列表] 失败:{}", throwable.getLocalizedMessage());
                return List.of();
            }

            @Override
            public List<OrderMemberChannelListVO> innerMemberChannelListByCondition(OrderMemberChannelListDTO dto, String source) {
                log.error("调用 [分销渠道结算-分销渠道列表] 失败:{}", throwable.getLocalizedMessage());
                return List.of();
            }

            @Override
            public String saveDistributionChannelOrder(DistributionChannelOrderDTO dto, String source) {
                log.error("调用 [保存渠道订单] 失败:{}", throwable.getLocalizedMessage());
                return null;
            }

            @Override
            public String saveBatchDistributionChannelOrder(DistributionChannelOrderBatchDTO dto, String source) {
                log.error("调用 [批量保存渠道订单] 失败:{}", throwable.getLocalizedMessage());
                return null;
            }
        };
    }
}

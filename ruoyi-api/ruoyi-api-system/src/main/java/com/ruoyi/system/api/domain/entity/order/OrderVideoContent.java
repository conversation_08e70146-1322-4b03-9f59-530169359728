package com.ruoyi.system.api.domain.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 视频_关联内容对象 order_video_content
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@ApiModel(value = "视频_关联内容对象 order_video_content")
@TableName("order_video_content")
@Data
public class OrderVideoContent implements Serializable {

    private static final long serialVersionUID = -4857478609267137939L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 视频id
     */
    @NotNull(message = "[视频id]不能为空")
    @ApiModelProperty(value = "视频id", required = true)
    @Excel(name = "视频id")
    private Long videoId;

    /**
     * 类型（1:拍摄建议（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求）
     */
    @ApiModelProperty(value = "类型（1:拍摄建议（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求）", required = true)
    @NotNull(message = "[类型]不能为空")
    @Excel(name = "类型", readConverterExp = "1:拍摄建议,2:模特要求")
    private Integer type;

    /**
     * 内容
     */
    @NotNull(message = "[内容]不能为空")
    @ApiModelProperty(value = "内容", required = true)
    @Excel(name = "内容")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String content;

    /**
     * 排序
     */
    @NotNull(message = "[排序]不能为空")
    @ApiModelProperty(value = "排序", required = true)
    @Excel(name = "排序")
    private Integer sort;

    @ApiModelProperty(value = "第一次变更内容")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String firstContent;

    //0未改1改
    private Integer firstEdit;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitBy;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    private Long submitById;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

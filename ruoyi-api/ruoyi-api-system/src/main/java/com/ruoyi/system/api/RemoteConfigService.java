package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.dto.system.EditMemberDiscountDTO;
import com.ruoyi.system.api.factory.RemoteConfigFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "RemoteConfigService", value = ServiceNameConstants.SYSTEM_SERVICE,
        fallbackFactory = RemoteConfigFallbackFactory.class)
public interface RemoteConfigService {
    /**
     * 根据参数键名查询参数值
     *
     * @param source    请求来源
     * @param configKey 参数键名
     * @return 结果
     */
    @GetMapping("/config/inner/configKey/{configKey}")
    String getConfigKey(@PathVariable("configKey") String configKey, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

//    /**
//     * 修改裂变渠道数据
//     *
//     * @param dto
//     * @param source
//     * @return
//     */
//    @PutMapping("/config/inner/editFissionMemberDiscount")
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    String editFissionMemberDiscount(@RequestBody EditMemberDiscountDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
//

    /**
     * 修改裂变渠道数据
     *
     * @param dto
     * @param source
     * @return
     */
    @PutMapping("/config/inner/editFissionMemberDiscount/V1")
    String editFissionMemberDiscountV1(@RequestBody EditFissionChannelDiscountDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 修改分销渠道数据
     *
     * @param dto
     * @param source
     * @return
     */
    @PutMapping("/config/inner/editMemberDiscount")
    String editMemberDiscount(@RequestBody EditMemberDiscountDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}

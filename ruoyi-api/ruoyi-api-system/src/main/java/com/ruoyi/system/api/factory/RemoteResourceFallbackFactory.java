package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteResourceService;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14 9:43
 */
public class RemoteResourceFallbackFactory implements FallbackFactory<RemoteResourceService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteResourceFallbackFactory.class);

    @Override
    public RemoteResourceService create(Throwable throwable) {
        log.error("业务资源表服务调用失败");
        return new RemoteResourceService() {
            @Override
            public List<BizResource> saveBatchBizResource(Collection<BizResource> bizResources, String source) {
                log.error("新增图片资源失败:{}", throwable.getMessage());
                return null;
            }
        };
    }
}

org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
  com.ruoyi.system.api.factory.RemoteUserFallbackFactory,\
  com.ruoyi.system.api.factory.RemoteLogFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteFileFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteLogisticFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteModelFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteResourceFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteTagFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteOrderFallbackFactory,\
  com.ruoyi.system.api.factory.RemoteBusinessAccountFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteConfigFallbackFactory, \
  com.ruoyi.system.api.config.WechatConfig, \
  com.ruoyi.system.api.config.OrderPayProperties, \
  com.ruoyi.system.api.config.WorkWeChatConfig, \
  com.ruoyi.system.api.config.WxEncryptConfig, \
  com.ruoyi.system.api.factory.RemoteAmazonFallbackFactory, \
  com.ruoyi.system.api.factory.RemoteDistributionChannelFactory,\
  com.ruoyi.system.api.factory.RemoteTranslateFallbackFactory,\
  com.ruoyi.system.api.factory.RemoteDictDataFallbackFactory,\
  com.ruoyi.system.api.factory.RemoteUserModelBlacklistFactory,\
  com.ruoyi.system.api.config.CustomerServiceProperties